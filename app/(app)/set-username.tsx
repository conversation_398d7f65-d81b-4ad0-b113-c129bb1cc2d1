import { useRouter } from 'expo-router';
import React, { useCallback } from 'react';
import useUpdateUserProfile from 'modules/profile/hooks/query/useUpdateUserProfile';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import userReader from 'core/readers/userReader';
import Username from 'modules/onboarding/pages/Username/Username';

const UsernameScreen = () => {
  const router = useRouter();
  const { updateUser } = useUpdateUserProfile();
  const { refreshCurrentUser, user: userData } = useSession();

  const isSignup = userReader.isSignUp(userData);
  const isGuest = userReader.isGuest(userData);

  const handleContinue = useCallback(
    async (username: string) => {
      try {
        showToast({
          type: TOAST_TYPE.LOADING,
          description: 'Verifying...',
        });
        const response = await updateUser({ username });
        if (response?.data?.updateUser) {
          await refreshCurrentUser(); // Refresh session to get latest user data including username
          hideToast();
        } else {
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Failed to update username.',
          });
          return;
        }

        router.push('/onboarding/referral');
      } catch (error) {
        console.error(
          'Failed to update username or navigate to referral:',
          error,
        );
        showToast({
          type: TOAST_TYPE.ERROR,
          description: 'An error occurred while updating username.',
        });
      }
    },
    [updateUser, router, userData, isSignup, isGuest, refreshCurrentUser],
  );

  return (
    <Username
      onContinue={handleContinue}
      currentStep={2}
      totalSteps={3}
      loggedInUser={userData}
    />
  );
};

export default UsernameScreen;
