import { useLocalSearchParams } from 'expo-router';
import ExploreFeature from 'modules/activation/pages/ExploreFeature';
import _get from 'lodash/get';
import _keys from 'lodash/keys';
import { EXPLORE_FEATURES } from 'modules/activation/constants/explore';
import _includes from 'lodash/includes';

const ExploreFeatureContainer = () => {
  const searchParams = useLocalSearchParams();

  const featureToExplore = _get(searchParams, 'feature');
  const allFeatures = _keys(EXPLORE_FEATURES);

  if (!_includes(allFeatures, featureToExplore)) {
    return <ExploreFeature feature={EXPLORE_FEATURES.ONLINE_DUELS} />;
  }

  return <ExploreFeature feature={featureToExplore} />;
};

export default ExploreFeatureContainer;
