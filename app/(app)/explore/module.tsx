import { useLocalSearchParams } from 'expo-router';
import _get from 'lodash/get';
import _keys from 'lodash/keys';
import { EXPLORE_MODULES } from 'modules/activation/constants/explore';
import _includes from 'lodash/includes';
import ExploreModule from 'modules/activation/pages/ExploreModule';
import ErrorView from 'atoms/ErrorView';

const ExploreModuleContainer = () => {
  const searchParams = useLocalSearchParams();

  const moduleToExplore = _get(searchParams, 'module');
  const allModules = _keys(EXPLORE_MODULES);

  if (!_includes(allModules, moduleToExplore)) {
    return <ErrorView errorMessage="Something went wrong!!" />;
  }

  return <ExploreModule module={moduleToExplore} />;
};

export default ExploreModuleContainer;
