import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import ErrorView from 'atoms/ErrorView';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import CrossMathPuzzleDummyPlayPage from 'modules/activation/pages/ExploreCrossMathPuzzle/components/CrossMathPuzzleDummyPlayPage';
import KenKenPuzzleDummyPlayPage from 'modules/activation/pages/ExploreKenKenPuzzle/components/KenKenPuzzleDummyPlayPage';

const PuzzlePlayContainer = () => {
  const searchParams = useLocalSearchParams();

  const { puzzleType }: { puzzleType: string } = searchParams ?? EMPTY_OBJECT;

  if (puzzleType === PUZZLE_TYPES.CROSS_MATH_PUZZLE) {
    return <CrossMathPuzzleDummyPlayPage />;
  }

  if (puzzleType === PUZZLE_TYPES.KEN_KEN_PUZZLE) {
    return <KenKenPuzzleDummyPlayPage />;
  }

  return <ErrorView errorMessage="Invalid puzzle type" />;
};

export default PuzzlePlayContainer;
