import OnboardingDetails, {
  ButtonType,
} from '@/src/modules/unauth/pages/MobileOnboardingScreen/components/OnboardingDetails/OnboardingDetails';
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';

import React, { useCallback } from 'react';
import { landingScreenData } from '@/src/modules/unauth/pages/SplashScreen/constants/SplashScreenData';
import { useRouter } from 'expo-router';

configureReanimatedLogger({ level: ReanimatedLogLevel.warn, strict: false });

const LandingScreen = () => {
  const router = useRouter();

  const getStartedOnPress = useCallback(() => {
    router.push('/duel-landing');
  }, [router]);

  const alreadyLoginOnPress = useCallback(() => {
    router.push({
      pathname: '/duel-landing',
      params: { alreadyLogin: true },
    });
  }, [router]);

  return (
    <OnboardingDetails
      riveResourceName="landing"
      buttonType={ButtonType.GET_STARTED}
      getStartedOnPress={getStartedOnPress}
      screenData={landingScreenData}
      alreadyLoginOnPress={alreadyLoginOnPress}
    />
  );
};

export default React.memo(LandingScreen);
