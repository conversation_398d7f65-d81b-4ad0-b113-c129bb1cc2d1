import { useEffect } from 'react';
import { Platform } from 'react-native';

const useEscKey = (onEscPress: () => void) => {
  useEffect(() => {
    if (Platform.OS !== 'web') return;
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onEscPress();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onEscPress]);
};

export default useEscKey;
