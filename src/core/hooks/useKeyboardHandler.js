import { useEffect, useState } from 'react';
import { Keyboard, Platform } from 'react-native';

/**
 * Custom hook to handle keyboard visibility and height changes
 * 
 * @returns {Object} Object containing keyboard state
 * @returns {boolean} isKeyboardVisible - Whether the keyboard is currently visible
 * @returns {number} keyboardHeight - Current height of the keyboard in pixels
 */
const useKeyboardHandler = () => {
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (e) => {
        setKeyboardVisible(true);
        setKeyboardHeight(e.endCoordinates.height);
      },
    );
    
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        setKeyboardHeight(0);
      },
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  return {
    isKeyboardVisible,
    keyboardHeight,
  };
};

export default useKeyboardHandler;
