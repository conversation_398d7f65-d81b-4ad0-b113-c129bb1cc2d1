import useChatStore from 'store/useChatStore';
import { useEffect, useMemo, useRef } from 'react';
import { ChatState, MESSAGE_SOURCE_TYPE } from 'store/useChatStore/types';
import _get from 'lodash/get';
import { MESSAGE_EVENTS } from 'modules/chatV2/constants/events';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useWebsocketStore from '@/src/store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from '../constants/websocket';

const useMessageHandler = () => {
  const { userId }: any = useSession();
  const { addMessage, fetchMessageGroups } = useChatStore(
    (state: ChatState) => ({
      addMessage: state.addMessage,
      fetchMessageGroups: state.fetchMessageGroups,
    }),
  );
  const channel = WEBSOCKET_CHANNELS.UserEvents(userId);

  const { lastMessage } = useWebsocketStore((state) => ({
    lastMessage: state.lastMessage,
  }));

  const incomingMessage = useMemo(() => {
    let _message = null;
    const data = _get(lastMessage, channel, EMPTY_OBJECT);
    const _event = _get(data, 'type', '');

    if (_event === MESSAGE_EVENTS.MESSAGE_PUBLISHED_EVENT) {
      const _payload = _get(data, 'event', EMPTY_OBJECT);
      _message = _get(_payload, 'message');
    }

    return _message;
  }, [lastMessage, channel]);

  const addMessageRef = useRef(addMessage);
  addMessageRef.current = addMessage;

  useEffect(() => {
    if (incomingMessage) {
      const incomingMessageGroupId = _get(incomingMessage, 'groupId');
      addMessageRef.current(
        incomingMessage,
        incomingMessageGroupId,
        MESSAGE_SOURCE_TYPE.OTHER,
      );
    }
  }, [incomingMessage]);

  useEffect(() => {
    fetchMessageGroups();
  }, [fetchMessageGroups]);
};

export default useMessageHandler;
