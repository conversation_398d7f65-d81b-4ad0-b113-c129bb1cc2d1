export const HOST = process.env.EXPO_PUBLIC_SERVER_HOST;

export const HOST_URL =
  HOST.startsWith('localhost') || HOST.startsWith('192.168')
    ? `http://${HOST}`
    : `https://${HOST}`;

export const SERVER_URL =
  HOST.startsWith('localhost') || HOST.startsWith('192.168')
    ? `http://${HOST}/api`
    : `https://${HOST}/api`;
export const WS_URL =
  HOST.startsWith('localhost') || HOST.startsWith('192.168')
    ? `ws://${HOST}/subscriptions`
    : `wss://${HOST}/subscriptions`;
export const WS_URL_2 =
  HOST.startsWith('localhost') || HOST.startsWith('192.168')
    ? `ws://${HOST}/ws`
    : `wss://${HOST}/ws`;
