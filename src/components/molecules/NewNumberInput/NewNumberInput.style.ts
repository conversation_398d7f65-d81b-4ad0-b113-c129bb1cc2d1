import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // paddingVertical: 10,
  },
  label: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    color: dark.colors.text,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  button: {
    width: 45,
    height: 45,
  },
  buttonText: {
    color: dark.colors.text,
    fontSize: 20,
    lineHeight: 22,
    fontFamily: 'Montserrat-600',
  },
  value: {
    fontFamily: 'Montserrat-800',
    fontSize: 12,
    color: dark.colors.text,
    minWidth: 60,
    textAlign: 'center',
  },
});

export default styles;
