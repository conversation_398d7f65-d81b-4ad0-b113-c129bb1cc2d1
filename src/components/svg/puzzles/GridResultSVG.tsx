import React from 'react';
import Svg, { Path } from 'react-native-svg';

const GridResultSVG = (props: any) => {
  return (
    <Svg
      width={1306}
      height={1306}
      viewBox="0 0 1306 1306"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path fill="#1E1E1E" d="M0 0H1306V1306H0z" />
      <Path
        d="M1159 147H147v1012h1012V147z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M1097.79 208.212H208.212v889.578h889.578V208.212z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M1040.52 265.475H265.475v775.055h775.045V265.475z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M987.211 318.789H318.789v668.422h668.422V318.789z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M937.846 368.154H368.154v569.692h569.692V368.154z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M892.431 413.569H413.569v478.862h478.862V413.569z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M850.965 455.035H455.036v395.93h395.929v-395.93z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M813.448 492.553H492.553v320.895h320.895V492.553z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M779.879 526.121H526.12V779.88h253.759V526.121z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M750.261 555.739H555.739v194.522h194.522V555.739z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M724.591 581.409H581.409v143.182h143.182V581.409z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M702.871 603.129h-99.742v99.742h99.742v-99.742z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M685.099 620.901h-64.198v64.198h64.198v-64.198z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M671.277 634.723h-36.554v36.554h36.554v-36.554z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M661.404 644.596h-16.808v16.808h16.808v-16.808z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M655.481 650.519h-4.961v4.962h4.961v-4.962z"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
      <Path
        d="M653.506 652.494h-1.012v1.012h1.012v-1.012zM652.494 653.506L147 1159M652.697 653.506L395.135 1159M652.899 653.506L572.836 1159M653.101 653.506L733.163 1159M653.303 653.506L910.865 1159M653.506 653.506L1159 1159M652.494 652.494L147 147M652.494 652.696L147 395.134M652.494 652.899L147 572.836M652.494 653.101L147 733.163M652.494 653.303L147 910.865M652.494 653.506L147 1159M653.506 652.494L1159 147M653.304 652.494L910.866 147M653.101 652.494L733.164 147M652.899 652.494L572.837 147M652.697 652.494L395.135 147M652.494 652.494L147 147M653.506 653.506L1159 1159M653.506 653.304L1159 910.866M653.506 653.101L1159 733.164M653.506 652.899L1159 572.837M653.506 652.697L1159 395.135M653.506 652.494L1159 147"
        stroke="#2E2F31"
        strokeWidth={0.5}
      />
    </Svg>
  );
};

export default GridResultSVG;
