import Svg, { Rect } from 'react-native-svg';
import React from 'react';

const CrossMathSVG = () => {
  return (
    <Svg
      width={135}
      height={140}
      viewBox="0 0 135 140"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Rect
        x={25.5}
        y={108.167}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 25.5 108.167)"
        fill="#A6A5F2"
      />
      <Rect
        x={51}
        y={152.335}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 51 152.335)"
        fill="#A6A5F2"
      />
      <Rect
        y={64}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 0 64)"
        fill="#6A69CC"
      />
      <Rect
        x={44.1672}
        y={38.5}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 44.167 38.5)"
        fill="#A6A5F2"
      />
      <Rect
        x={69.6672}
        y={82.6673}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 69.667 82.667)"
        fill="#6A69CC"
      />
      <Rect
        x={95.1672}
        y={126.835}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 95.167 126.835)"
        fill="#A6A5F2"
      />
      <Rect
        x={88.3345}
        y={13}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 88.335 13)"
        fill="#A6A5F2"
      />
      <Rect
        x={113.334}
        y={56.3013}
        width={48}
        height={48}
        rx={8}
        transform="rotate(-30 113.334 56.301)"
        fill="#A6A5F2"
      />
    </Svg>
  );
};

export default React.memo(CrossMathSVG);
