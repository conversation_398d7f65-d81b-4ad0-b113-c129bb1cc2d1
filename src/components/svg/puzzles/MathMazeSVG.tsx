import Svg, { Rect, <PERSON> } from 'react-native-svg';
import React from 'react';

const MathMazeSVG = () => {
  return (
    <Svg
      width={130}
      height={140}
      viewBox="0 0 130 140"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Rect
        x={46.1672}
        y={27.5}
        width={48}
        height={48}
        rx={24}
        transform="rotate(-30 46.167 27.5)"
        fill="#A6A5F2"
      />
      <Rect
        x={71.6672}
        y={71.6673}
        width={48}
        height={48}
        rx={24}
        transform="rotate(-30 71.667 71.667)"
        fill="#A6A5F2"
      />
      <Path
        d="M27.713 38c7.652-4.418 17.438-1.796 21.856 5.856l37 64.086c3.314 5.74 10.653 7.706 16.393 4.393l26.846-15.5a8.001 8.001 0 0110.929 2.928l16 27.713a8 8 0 01-2.929 10.928l-71.88 41.5A8 8 0 0171 176.976L8 67.856C3.582 60.204 6.204 50.418 13.856 46l13.857-8z"
        fill="#F2E5A5"
      />
      <Rect
        x={90.3345}
        y={2}
        width={48}
        height={48}
        rx={24}
        transform="rotate(-30 90.335 2)"
        fill="#A6A5F2"
      />
      <Rect
        x={115.334}
        y={45.3013}
        width={48}
        height={48}
        rx={24}
        transform="rotate(-30 115.334 45.301)"
        fill="#A6A5F2"
      />
    </Svg>
  );
};

export default React.memo(MathMazeSVG);
