import Svg, { <PERSON>, <PERSON> } from 'react-native-svg';
import React from 'react';

const KenKenSVG = () => (
  <Svg
    width={130}
    height={135}
    viewBox="0 0 130 135"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      transform="rotate(-30 141.196 37.833)"
      fill="#1E1E1E"
      d="M0 0H150V150H0z"
    />
    <Mask id="a" fill="#fff">
      <Path d="M136.237 113.635l44.167-25.5 24.5 42.435L75 205.57l-24.5-42.435 44.167-25.5-25-43.301 41.57-24 25 43.301z" />
    </Mask>
    <Path
      d="M136.237 113.635l-.867.5.5.866.867-.5-.5-.866zm44.167-25.5l.866-.5-.5-.866-.866.5.5.866zm24.5 42.435l.5.867.866-.5-.5-.867-.866.5zM75 205.57l-.866.5.5.867.866-.5-.5-.867zm-24.5-42.435l-.5-.866-.866.5.5.866.866-.5zm44.167-25.5l.5.866.866-.5-.5-.866-.866.5zm-25-43.301l-.5-.866-.866.5.5.866.866-.5zm41.57-24l.866-.5-.5-.866-.866.5.5.866zm25 43.301l.5.866 44.167-25.5-.5-.866-.5-.866-44.167 25.5.5.866zm44.167-25.5l-.866.5 24.5 42.435.866-.5.866-.5-24.5-42.435-.866.5zm24.5 42.435l-.5-.866-129.904 75 .5.866.5.867 129.904-75-.5-.867zM75 205.57l.866-.5-24.5-42.435-.866.5-.866.5 24.5 42.435.866-.5zm-24.5-42.435l.5.866 44.167-25.5-.5-.866-.5-.866L50 162.269l.5.866zm44.167-25.5l.866-.5-25-43.301-.866.5-.866.5 25 43.301.866-.5zm-25-43.301l.5.866 41.57-24-.5-.866-.5-.866-41.57 24 .5.866zm41.57-24l-.867.5 25 43.301.867-.5.866-.5-25-43.301-.866.5z"
      fill="#6A69CC"
      mask="url(#a)"
    />
    <Mask id="b" fill="#fff">
      <Path d="M85.737 26.167l24.5 42.435-43.302 25 25 43.301-42.435 24.5L0 75.667l85.737-49.5z" />
    </Mask>
    <Path
      d="M85.737 26.167l.866-.5-.5-.866-.866.5.5.866zm24.5 42.435l.5.866.866-.5-.5-.866-.866.5zm-43.302 25l-.5-.866-.866.5.5.866.866-.5zm25 43.301l.5.866.866-.5-.5-.866-.866.5zm-42.435 24.5l-.866.5.5.866.866-.5-.5-.866zM0 75.667l-.5-.866-.866.5.5.866.866-.5zm85.737-49.5l-.866.5 24.499 42.435.867-.5.866-.5-24.5-42.435-.866.5zm24.5 42.435l-.5-.866-43.302 25 .5.866.5.866 43.302-25-.5-.866zm-43.302 25l-.866.5 25 43.301.866-.5.866-.5-25-43.301-.866.5zm25 43.301l-.5-.866L49 160.537l.5.866.5.866 42.435-24.5-.5-.866zm-42.435 24.5l.866-.5-49.5-85.736-.866.5-.866.5 49.5 85.736.866-.5zM0 75.667l.5.866 85.737-49.5-.5-.866-.5-.866L-.5 74.8l.5.866z"
      fill="#6A69CC"
      mask="url(#b)"
    />
    <Mask id="c" fill="#fff">
      <Path d="M179.404 86.403l-42.435 24.5-49.5-85.736 42.435-24.5 49.5 85.736z" />
    </Mask>
    <Path
      d="M179.404 86.403l.5.866.866-.5-.5-.866-.866.5zm-42.435 24.5l-.867.5.5.866.867-.5-.5-.866zm-49.5-85.736l-.5-.866-.866.5.5.866.866-.5zm42.435-24.5l.866-.5-.5-.866-.866.5.5.866zm49.5 85.736l-.5-.866-42.435 24.5.5.866.5.866 42.435-24.5-.5-.866zm-42.435 24.5l.866-.5-49.5-85.736-.866.5-.866.5 49.499 85.736.867-.5zm-49.5-85.736l.5.866 42.435-24.5-.5-.866-.5-.866-42.435 24.5.5.866zm42.435-24.5l-.866.5 49.5 85.736.866-.5.866-.5L130.77.167l-.866.5z"
      fill="#6A69CC"
      mask="url(#c)"
    />
    <Mask id="d" fill="#fff">
      <Path d="M1.366 76.033l41.57-24 23.5 40.703-41.57 24-23.5-40.703z" />
    </Mask>
    <Path
      d="M42.935 52.033l-.866.5 23.5 40.703.866-.5.866-.5-23.5-40.703-.866.5z"
      fill="#fff"
      fillOpacity={0.2}
      mask="url(#d)"
    />
    <Mask id="e" fill="#fff">
      <Path d="M48.866 158.305l-24-41.569 40.703-23.5 24 41.569-40.703 23.5z" />
    </Mask>
    <Path
      d="M24.866 116.736l.5.866 40.703-23.5-.5-.866-.5-.866-40.703 23.5.5.866z"
      fill="#fff"
      fillOpacity={0.2}
      mask="url(#e)"
    />
    <Mask id="f" fill="#fff">
      <Path d="M136.834 108.671l-24-41.57 40.704-23.5 24 41.57-40.704 23.5z" />
    </Mask>
    <Path
      d="M112.834 67.102l.5.866 40.704-23.5-.5-.866-.5-.866-40.704 23.5.5.866z"
      fill="#fff"
      fillOpacity={0.2}
      mask="url(#f)"
    />
    <Mask id="g" fill="#fff">
      <Path d="M110.371 70.834l25.5 44.167-39.838 23-25.5-44.167 39.838-23z" />
    </Mask>
    <Path
      d="M135.871 115.001l-.5-.866-39.838 23 .5.866.5.866 39.838-23-.5-.866z"
      fill="#fff"
      fillOpacity={0.2}
      mask="url(#g)"
    />
  </Svg>
);

export default React.memo(KenKenSVG);
