import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const FistSVG = (props: any) => (
  <Svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M5.46819 14.203C5.24152 14.203 5.05152 14.1263 4.89819 13.973C4.74486 13.8197 4.66819 13.6297 4.66819 13.403V11.5203H11.4732V13.403C11.4732 13.6297 11.3965 13.8197 11.2432 13.973C11.0899 14.1263 10.8999 14.203 10.6732 14.203H5.46819ZM13.2145 5.59998V7.43332C13.2145 7.53332 13.2006 7.63054 13.1729 7.72498C13.1451 7.81943 13.1201 7.9111 13.0979 7.99998L12.6364 9.64632C12.5459 9.95843 12.37 10.2154 12.1087 10.4173C11.8474 10.6193 11.5551 10.7203 11.2319 10.7203H4.76819C4.43386 10.7203 4.13324 10.6193 3.86636 10.4173C3.59947 10.2154 3.43191 9.95843 3.36369 9.64632L2.83736 7.77032C2.8028 7.67898 2.78552 7.58332 2.78552 7.48332V3.71298C2.78552 3.25743 2.94386 2.87132 3.26052 2.55465C3.57719 2.23798 3.9633 2.07965 4.41886 2.07965H9.69852C10.1542 2.07965 10.5404 2.23798 10.857 2.55465C11.1736 2.87132 11.3319 3.25743 11.3319 3.71298V5.25148C11.3759 5.05615 11.4777 4.89598 11.6374 4.77098C11.7969 4.64587 11.9837 4.58332 12.1979 4.58332C12.4811 4.58332 12.7213 4.68198 12.9185 4.87932C13.1159 5.07654 13.2145 5.31676 13.2145 5.59998ZM6.41886 6.51298H7.69852C7.92074 6.51298 8.10963 6.43521 8.26519 6.27965C8.42074 6.1241 8.49852 5.93521 8.49852 5.71298C8.49852 5.49076 8.42074 5.30187 8.26519 5.14632C8.10963 4.99076 7.92074 4.91298 7.69852 4.91298H6.41886C6.19663 4.91298 6.00774 4.99076 5.85219 5.14632C5.69663 5.30187 5.61886 5.49076 5.61886 5.71298C5.61886 5.93521 5.69663 6.1241 5.85219 6.27965C6.00774 6.43521 6.19663 6.51298 6.41886 6.51298Z"
      fill="#E8EAED"
    />
  </Svg>
);

export default FistSVG;
