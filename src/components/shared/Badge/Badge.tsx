import React from 'react';
import dark from '@/src/core/constants/themes/dark';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

const BadgeStyles = StyleSheet.create({
  badge: {
    position: 'absolute',
    top: 7,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: dark.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeInner: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: dark.colors.errorDark,
  },
});

const Badge = ({
  containerStyle,
}: {
  containerStyle?: StyleProp<ViewStyle>;
}) => {
  return (
    <View style={[BadgeStyles.badge, containerStyle]}>
      <View style={BadgeStyles.badgeInner} />
    </View>
  );
};

export default React.memo(Badge);
