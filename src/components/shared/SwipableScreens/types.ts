import { ReactNode } from 'react';
import { ViewStyle } from 'react-native';

export interface SwipableScreen {
  id: string;
  component: ReactNode;
  title?: string;
  swipeable?: boolean;
}

export interface SwipableScreensProps {
  screens: SwipableScreen[];
  initialScreenIndex?: number;
  onScreenChange?: (screenIndex: number, screen: SwipableScreen) => void;
  containerStyle?: ViewStyle;
  showIndicator?: boolean;
  indicatorPosition?: 'bottom' | 'top';
  swipeThreshold?: number;
  animationDuration?: number;
  disabled?: boolean;
  enableHapticFeedback?: boolean;
  hapticIntensity?: 'Light' | 'Medium' | 'Heavy';
}

export interface SwipeIndicatorProps {
  isVisible: boolean;
  position: 'bottom' | 'top';
  onPress?: () => void;
}
