import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import SwipableScreens from './SwipableScreens';
import { SwipableScreen } from './types';

// Example screen components
const Screen1 = () => (
  <View
    style={[exampleStyles.screen, { backgroundColor: dark.colors.background }]}
  >
    <Text style={exampleStyles.title}>Screen 1</Text>
    <Text style={exampleStyles.subtitle}>This is the first screen</Text>
    <Text style={exampleStyles.instruction}>
      Swipe up to see the next screen
    </Text>
  </View>
);

const Screen2 = () => (
  <View
    style={[exampleStyles.screen, { backgroundColor: dark.colors.primary }]}
  >
    <Text style={exampleStyles.title}>Screen 2</Text>
    <Text style={exampleStyles.subtitle}>This is the second screen</Text>
    <Text style={exampleStyles.instruction}>
      Swipe up for screen 3 or down for screen 1
    </Text>
  </View>
);

const Screen3 = () => (
  <View
    style={[exampleStyles.screen, { backgroundColor: dark.colors.tertiary }]}
  >
    <Text style={exampleStyles.title}>Screen 3</Text>
    <Text style={exampleStyles.subtitle}>This is the third screen</Text>
    <Text style={exampleStyles.instruction}>Swipe down to go back</Text>
  </View>
);

const SwipableScreensExample: React.FC = () => {
  const screens: SwipableScreen[] = [
    {
      id: 'screen1',
      component: <Screen1 />,
      title: 'First Screen',
    },
    {
      id: 'screen2',
      component: <Screen2 />,
      title: 'Second Screen',
    },
    {
      id: 'screen3',
      component: <Screen3 />,
      title: 'Third Screen',
    },
  ];

  const handleScreenChange = (screenIndex: number, screen: SwipableScreen) => {
    console.log(`Changed to screen: ${screen.title} (index: ${screenIndex})`);
  };

  return (
    <SwipableScreens
      screens={screens}
      initialScreenIndex={0}
      onScreenChange={handleScreenChange}
      showIndicator
      indicatorPosition="bottom"
      swipeThreshold={50}
      animationDuration={300}
      enableHapticFeedback={true}
      hapticIntensity="Medium"
    />
  );
};

const exampleStyles = StyleSheet.create({
  screen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    height: '100%',
  },
  title: {
    fontSize: 32,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    marginBottom: 24,
    textAlign: 'center',
  },
  instruction: {
    fontSize: 14,
    fontFamily: 'Montserrat-400',
    color: dark.colors.textDark,
    textAlign: 'center',
    opacity: 0.8,
  },
  note: {
    fontSize: 12,
    fontFamily: 'Montserrat-400',
    color: dark.colors.secondary,
    textAlign: 'center',
    marginTop: 16,
    fontStyle: 'italic',
  },
});

export default SwipableScreensExample;
