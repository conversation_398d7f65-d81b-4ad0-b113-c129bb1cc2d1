# SwipableScreens Component

A generic React Native component that allows users to swipe between multiple screens/components with smooth animations and visual indicators.

## Features

- ✅ Swipe up/down gesture support using react-native-gesture-handler
- ✅ Smooth animations between screens
- ✅ Visual indicator with arrow icon showing swipe direction
- ✅ **Haptic feedback** on swipes and indicator taps
- ✅ Customizable swipe threshold and animation duration
- ✅ TypeScript support
- ✅ Follows project's dark theme styling
- ✅ Configurable indicator position (top/bottom)
- ✅ Optional screen change callbacks
- ✅ Disable functionality when needed
- ✅ Customizable haptic feedback intensity

## Usage

### Basic Usage

```tsx
import SwipableScreens from '@/src/components/shared/SwipableScreens';
import { SwipableScreen } from '@/src/components/shared/SwipableScreens/types';

const MyComponent = () => {
  const screens: SwipableScreen[] = [
    {
      id: 'screen1',
      component: <YourFirstScreen />,
      title: 'First Screen',
    },
    {
      id: 'screen2',
      component: <YourSecondScreen />,
      title: 'Second Screen',
    },
    {
      id: 'screen3',
      component: <YourThirdScreen />,
      title: 'Third Screen',
    },
  ];

  return (
    <SwipableScreens
      screens={screens}
      initialScreenIndex={0}
      onScreenChange={(index, screen) => {
        console.log(`Changed to: ${screen.title}`);
      }}
    />
  );
};
```

### Advanced Usage

```tsx
<SwipableScreens
  screens={screens}
  initialScreenIndex={1}
  onScreenChange={handleScreenChange}
  showIndicator={true}
  indicatorPosition="bottom"
  swipeThreshold={75}
  animationDuration={400}
  disabled={false}
  enableHapticFeedback={true}
  hapticIntensity="Medium"
  containerStyle={{ backgroundColor: 'transparent' }}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `screens` | `SwipableScreen[]` | **Required** | Array of screen objects to display |
| `initialScreenIndex` | `number` | `0` | Index of the initial screen to show |
| `onScreenChange` | `(index: number, screen: SwipableScreen) => void` | `undefined` | Callback fired when screen changes |
| `containerStyle` | `ViewStyle` | `undefined` | Custom styles for the container |
| `showIndicator` | `boolean` | `true` | Whether to show the swipe indicator |
| `indicatorPosition` | `'bottom' \| 'top'` | `'bottom'` | Position of the swipe indicator |
| `swipeThreshold` | `number` | `50` | Minimum distance to trigger a swipe |
| `animationDuration` | `number` | `300` | Duration of screen transition animation (ms) |
| `disabled` | `boolean` | `false` | Disable all swipe interactions |
| `enableHapticFeedback` | `boolean` | `true` | Enable haptic feedback on swipes |
| `hapticIntensity` | `'Light' \| 'Medium' \| 'Heavy'` | `'Medium'` | Intensity of haptic feedback |

## SwipableScreen Interface

```tsx
interface SwipableScreen {
  id: string;          // Unique identifier for the screen
  component: ReactNode; // The React component to render
  title?: string;      // Optional title for the screen
}
```

## Gestures

- **Swipe Up**: Navigate to the next screen (if available)
- **Swipe Down**: Navigate to the previous screen (if available)
- **Tap Indicator**: Navigate in the direction indicated by the arrow

## Haptic Feedback

The component provides haptic feedback to enhance user experience:

- **Screen Changes**: Medium intensity haptic feedback when successfully swiping between screens
- **Indicator Taps**: Light haptic feedback when tapping the swipe indicator
- **Customizable**: Control haptic feedback with `enableHapticFeedback` and `hapticIntensity` props
- **Smart**: No haptic feedback when snapping back to the same screen (failed swipe)

## Styling

The component uses the project's dark theme by default. You can customize the appearance by:

1. Modifying `SwipableScreens.style.ts` for global changes
2. Using the `containerStyle` prop for container-specific styling
3. Styling individual screen components as needed

## Example

See `SwipableScreensExample.tsx` for a complete working example with three different screens.

## Dependencies

- `react-native-gesture-handler` (already available in the project)
- `@/src/components/atoms/Icon` (project's icon component)
- `@/src/components/atoms/Pressable` (project's pressable component)

## Notes

- **Each screen takes up the full screen height** - Every screen component you pass will occupy the entire screen height, not divided among screens
- **Absolute positioning** - Screens are positioned absolutely and translated smoothly between each other
- The component automatically handles screen boundaries (can't swipe beyond first/last screen)
- The indicator only shows when there's a screen available in the swipe direction
- Animations use native driver for better performance
- **Fixed Issue**: Previously screens were stacked vertically dividing height - now each screen gets full height
