import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  screenContainer: {
    width: '100%',
  },
  indicatorContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  indicatorContainerBottom: {
    bottom: 120,
  },
  indicatorContainerTop: {
    top: 20,
  },
  indicator: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicatorText: {
    color: withOpacity(dark.colors.textLight, 0.6),
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  screenWrapper: {
    flex: 1,
  },
  animatedContainer: {
    flex: 1,
    overflow: 'hidden',
  },
});

export default styles;
