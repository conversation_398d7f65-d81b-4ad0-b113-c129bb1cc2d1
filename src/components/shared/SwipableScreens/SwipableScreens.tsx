import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Dimensions, Text, View } from 'react-native';
import {
  PanGestureHandler,
  State as GestureState,
} from 'react-native-gesture-handler';
import 'atoms/gesture-handler';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import Pressable from 'atoms/Pressable';
import useHaptics from 'core/hooks/useHaptics';
import { withOpacity } from '@/src/core/utils/colorUtils';
import dark from 'core/constants/themes/dark';
import _map from 'lodash/map';
import _size from 'lodash/size';
import * as Animatable from 'react-native-animatable';
import _toNumber from 'lodash/toNumber';
import { SwipableScreensProps, SwipeIndicatorProps } from './types';
import styles from './SwipableScreens.style';

const HEADER_HEIGHT = 84;

const SCREEN_HEIGHT =
  _toNumber(Dimensions.get('window').height) - HEADER_HEIGHT; // HEADER HEIGHT = 84;

export const SwipeIndicator: React.FC<SwipeIndicatorProps> = ({
  isVisible,
  position,
  onPress,
}) => {
  const opacity = useRef(new Animated.Value(isVisible ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: isVisible ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isVisible, opacity]);

  const iconName = position === 'bottom' ? 'chevron-up' : 'chevron-down';

  if (!isVisible) return null;

  return (
    <Animatable.View
      style={[
        styles.indicatorContainer,
        position === 'bottom'
          ? styles.indicatorContainerBottom
          : styles.indicatorContainerTop,
      ]}
      pointerEvents={isVisible ? 'auto' : 'none'}
      animation="slideInUp"
      duration={600}
      delay={600}
    >
      <Pressable onPress={onPress} style={styles.indicator}>
        <Icon
          type={ICON_TYPES.IONICON as any}
          name={iconName}
          size={16}
          color={withOpacity(dark.colors.textLight, 0.4)}
        />
        <Text style={styles.indicatorText}>
          {position === 'bottom' ? 'Swipe up' : 'Swipe down'}
        </Text>
      </Pressable>
    </Animatable.View>
  );
};

const SwipableScreens: React.FC<SwipableScreensProps> = ({
  screens,
  initialScreenIndex = 0,
  onScreenChange,
  containerStyle,
  showIndicator = true,
  indicatorPosition = 'bottom',
  swipeThreshold = 50,
  animationDuration = 300,
  disabled = false,
  enableHapticFeedback = true,
  hapticIntensity = 'Medium',
}) => {
  const [currentScreenIndex, setCurrentScreenIndex] =
    useState(initialScreenIndex);
  const translateY = useRef(
    new Animated.Value(-initialScreenIndex * SCREEN_HEIGHT),
  ).current;
  const gestureRef = useRef(null);
  const { triggerHaptic } = useHaptics();

  // Update translateY when initialScreenIndex changes
  useEffect(() => {
    translateY.setValue(-initialScreenIndex * SCREEN_HEIGHT);
    setCurrentScreenIndex(initialScreenIndex);
  }, [initialScreenIndex, translateY]);

  const canSwipeUp = currentScreenIndex < screens.length - 1;
  const canSwipeDown = currentScreenIndex > 0;

  const animateToScreen = useCallback(
    (screenIndex: number, withHaptic = true) => {
      const targetY = -screenIndex * SCREEN_HEIGHT;

      // Trigger haptic feedback for screen change
      if (withHaptic && enableHapticFeedback) {
        triggerHaptic(hapticIntensity);
      }

      setCurrentScreenIndex(screenIndex);
      onScreenChange?.(screenIndex, screens[screenIndex]);

      Animated.timing(translateY, {
        toValue: targetY,
        duration: animationDuration,
        useNativeDriver: true,
      }).start();
    },
    [
      translateY,
      animationDuration,
      onScreenChange,
      screens,
      triggerHaptic,
      enableHapticFeedback,
      hapticIntensity,
    ],
  );

  const handleSwipeUp = useCallback(() => {
    if (canSwipeUp && !disabled) {
      animateToScreen(currentScreenIndex + 1);
    }
  }, [canSwipeUp, disabled, animateToScreen, currentScreenIndex]);

  const handleSwipeDown = useCallback(() => {
    if (canSwipeDown && !disabled) {
      animateToScreen(currentScreenIndex - 1);
    }
  }, [canSwipeDown, disabled, animateToScreen, currentScreenIndex]);

  const onGestureEvent = useCallback(
    (event: any) => {
      const { translationY } = event.nativeEvent;
      const currentScreenOffset = -currentScreenIndex * SCREEN_HEIGHT;
      const newTranslateY = currentScreenOffset + translationY;

      const clampedTranslateY = Math.max(
        -(_size(screens) - 1) * SCREEN_HEIGHT,
        Math.min(0, newTranslateY),
      );

      translateY.setValue(clampedTranslateY);
    },
    [translateY, currentScreenIndex, screens],
  );

  const onHandlerStateChange = useCallback(
    (event: any) => {
      if (disabled) return;

      const { state, translationY, velocityY } = event.nativeEvent;

      if (state === GestureState.END) {
        const shouldSwipeUp =
          translationY < -swipeThreshold || velocityY < -500;
        const shouldSwipeDown =
          translationY > swipeThreshold || velocityY > 500;

        if (shouldSwipeUp && canSwipeUp) {
          handleSwipeUp();
        } else if (shouldSwipeDown && canSwipeDown) {
          handleSwipeDown();
        } else {
          // Snap back to current screen without haptic feedback
          animateToScreen(currentScreenIndex, false);
        }
      }
    },
    [
      disabled,
      swipeThreshold,
      canSwipeUp,
      canSwipeDown,
      handleSwipeUp,
      handleSwipeDown,
      animateToScreen,
      currentScreenIndex,
    ],
  );

  const handleIndicatorPress = useCallback(() => {
    // Light haptic feedback for indicator tap
    if (enableHapticFeedback) {
      triggerHaptic('Light');
    }

    if (indicatorPosition === 'bottom' && canSwipeUp) {
      handleSwipeUp();
    } else if (indicatorPosition === 'top' && canSwipeDown) {
      handleSwipeDown();
    }
  }, [
    indicatorPosition,
    canSwipeUp,
    canSwipeDown,
    handleSwipeUp,
    handleSwipeDown,
    triggerHaptic,
    enableHapticFeedback,
  ]);

  const shouldShowIndicator =
    showIndicator &&
    ((indicatorPosition === 'bottom' && canSwipeUp) ||
      (indicatorPosition === 'top' && canSwipeDown));

  return (
    <View style={[styles.container, containerStyle]}>
      <PanGestureHandler
        ref={gestureRef}
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        enabled={!disabled && (screens[currentScreenIndex]?.swipeable ?? true)}
        activeOffsetY={[-10, 10]}
        failOffsetX={[-50, 50]}
      >
        <Animated.View style={styles.animatedContainer}>
          <Animated.View
            style={[
              styles.screenWrapper,
              {
                transform: [{ translateY }],
              },
            ]}
          >
            {_map(screens, (screen, index) => {
              const inputRange = [
                -(index + 1) * SCREEN_HEIGHT,
                -index * SCREEN_HEIGHT,
                -(index - 1) * SCREEN_HEIGHT,
              ];

              const opacity = translateY.interpolate({
                inputRange,
                outputRange: [0.1, 1, 0.1],
                extrapolate: 'clamp',
              });

              return (
                <Animated.View
                  key={screen.id}
                  style={[
                    styles.screenContainer,
                    {
                      position: 'absolute',
                      top: index * SCREEN_HEIGHT,
                      left: 0,
                      right: 0,
                      height: SCREEN_HEIGHT,
                      opacity,
                    },
                  ]}
                >
                  {screen.component}
                </Animated.View>
              );
            })}
          </Animated.View>
          <SwipeIndicator
            isVisible={shouldShowIndicator}
            position={indicatorPosition}
            onPress={handleIndicatorPress}
          />
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

export default React.memo(SwipableScreens);
