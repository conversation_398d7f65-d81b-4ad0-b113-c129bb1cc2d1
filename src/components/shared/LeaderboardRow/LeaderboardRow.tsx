import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { Text, View } from 'react-native';
import { getFormattedTimeWithMS } from '@/src/core/utils/general';
import React, { useCallback, useMemo } from 'react';
import FirstRankSvg from '@/src/components/svg/Ranks/FirstRankSvg';
import SecondRankSvg from '@/src/components/svg/Ranks/SecondRankSvg';
import ThirdRank from '@/src/components/svg/Ranks/ThirdRank';
import _includes from 'lodash/includes';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import userReader from '@/src/core/readers/userReader';
import styles from './LeaderboardRow.style';
import UserImage from '../../atoms/UserImage';
import puzzleLeaderboardReader, {
  CompletionStatus,
} from '@/src/modules/puzzles/readers/puzzleLeaderboardReader';
import _isNil from 'lodash/isNil';
import ChallengeButton from './components/ChallengeButton';
import { LeaderboardRowProps } from './types';
import { BADGES_DETAILS } from '@/src/modules/profile/constants/badges';

const LeaderBoardRankIcon = React.memo(({ rank }: { rank: number }) => {
  switch (rank) {
    case 1:
      return <FirstRankSvg />;
    case 2:
      return <SecondRankSvg />;
    case 3:
      return <ThirdRank />;
    default:
      return null;
  }
});

const LeaderboardRow: React.FC<LeaderboardRowProps> = (props) => {
  const { index, participant, puzzleType } = props;
  const { isMobile: isCompactMode } = useMediaQuery();
  const { user } = useSession();
  const completionStatus =
    puzzleLeaderboardReader.completionStatus(participant);
  const rankedUser = puzzleLeaderboardReader.user(participant);
  const rank = puzzleLeaderboardReader.rank(participant);
  const time = puzzleLeaderboardReader.timeSpent(participant);
  const userId = userReader.id(rankedUser);
  const badge = userReader.badge(rankedUser) || 'NOVICE';
  const profileImageUrl = userReader.profileImageUrl(rankedUser);
  const name = userReader.displayName(rankedUser);
  const username = userReader.username(rankedUser);
  const rating = userReader.puzzleRating(rankedUser);
  const isCurrentUser = user?._id === userId;

  const onChallengePress = useCallback(NULL_FUN, []);

  const renderRank = useCallback(
    () => (
      <View style={styles.rankColumn}>
        {isCurrentUser && !_includes([1, 2, 3], rank) ? (
          <View style={styles.rankCircle}>
            <Text style={styles.rankCircleText}>{rank}</Text>
          </View>
        ) : (
          <Text style={[styles.rankLabel]}>
            {_includes([1, 2, 3], rank) ? (
              <LeaderBoardRankIcon rank={rank} />
            ) : (
              rank
            )}
          </Text>
        )}
      </View>
    ),
    [rank, isCurrentUser],
  );

  const adaptedUser = useMemo(
    () => ({
      _id: userId ?? '',
      profileImageUrl: profileImageUrl ?? '',
    }),
    [userId, profileImageUrl],
  );

  return (
    <View
      key={index}
      style={[
        styles.rowContainer,
        isCompactMode && styles.compactRowContainer,
        isCurrentUser && rank === 1 && styles.currUserFirstBg,
        isCurrentUser && styles.currUserBg,
      ]}
    >
      {_isNil(rank) ? null : renderRank()}
      <View style={styles.profileInfoColumn}>
        <View style={styles.profileImageContainer}>
          <UserImage user={adaptedUser} size={50} rounded />
        </View>
        <View style={styles.badgeLabelContainer}>
          <Text style={styles.badgeLabel}>
            {BADGES_DETAILS[badge].shortForm}
          </Text>
        </View>
        <View style={[styles.usernameContainer]}>
          <Text style={styles.rowLabel} numberOfLines={1}>
            {name ?? username}
          </Text>
          <Text style={[styles.rating]}>
            {rating} - {puzzleType}
          </Text>
        </View>
      </View>
      <View
        style={[
          styles.timeTakenColumn,
          isCurrentUser && styles.currUserTimeBg,
          rank === 1 && styles.firstPlace,
        ]}
      >
        {completionStatus === CompletionStatus.NOT_PLAYED ? (
          <ChallengeButton onPress={onChallengePress} label="CHALLENGE" />
        ) : (
          <Text
            style={[
              styles.rowLabelScore,
              (rank === 1 || isCurrentUser) && styles.firstPlaceLabel,
            ]}
          >
            {getFormattedTimeWithMS(time)}
          </Text>
        )}
      </View>
    </View>
  );
};

export default React.memo(LeaderboardRow);
