import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  sendChallenge: {
    borderRadius: 10,
    backgroundColor: dark.colors.background,
    borderWidth: 1,
    borderColor: dark.colors.secondaryButtonBorder,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,

    padding: 8,
  },
  sendChallengeText: {
    color: dark.colors.textLight,
    fontSize: 10,
    fontFamily: 'Montserrat-700',
  },
});

export default styles;
