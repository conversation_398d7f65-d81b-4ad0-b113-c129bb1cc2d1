import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

export const styles = StyleSheet.create({
  progressContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  progressStep: {
    flex: 1,
    height: 8,
    borderRadius: 40,
    backgroundColor: dark.colors.tertiary, // Default gray color for all steps
    overflow: 'hidden',
  },
  progressStepCompleted: {
    backgroundColor: dark.colors.tertiary, // Base background
    position: 'relative',
  },
  progressStepCompletedBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 2,
    // Gradient colors: rgba(169, 249, 158, 1) to rgba(0, 217, 255, 1)
    backgroundColor: 'transparent',
    // This will be replaced with LinearGradient component
  },
  progressStepActive: {
    backgroundColor: dark.colors.tertiary, // Base gray color (80%)
    position: 'relative',
  },
  progressStepActiveFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: '25%', // Only 25% filled with gradient
    backgroundColor: 'transparent', // Gradient will handle the color
    borderRadius: 40,
  },
  progressStepInactive: {
    backgroundColor: dark.colors.progressBarInactive, // Gray color for incomplete steps
  },
});
