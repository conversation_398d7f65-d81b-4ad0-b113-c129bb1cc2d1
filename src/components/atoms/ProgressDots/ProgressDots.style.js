import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { useMemo } from 'react';

const createStyles = ({ dotSize, spacing, activeColor, inactiveColor }) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    dot: {
      width: dotSize,
      height: dotSize,
      borderRadius: dotSize / 2,
    },
    activeDot: {
      backgroundColor: activeColor,
    },
    inactiveDot: {
      backgroundColor: inactiveColor,
      borderWidth: 1,
      borderColor: dark.colors.textLight
    },
    connectingLine: {
      height: 2,
      width: spacing, // Use spacing directly for the line width
      marginHorizontal: spacing / 2,
    },
    activeConnectingLine: {
      backgroundColor: activeColor,
    },
    inactiveConnectingLine: {
      backgroundColor: inactiveColor,
    },
    dotGap: {
      width: spacing,
    },
    pill: {
      height: dotSize,
      borderRadius: dotSize / 2,
    },
  });

export const useProgressDotsStyles = (props) => {
  const styles = useMemo(() => createStyles(props), [props]);
  return styles;
};
