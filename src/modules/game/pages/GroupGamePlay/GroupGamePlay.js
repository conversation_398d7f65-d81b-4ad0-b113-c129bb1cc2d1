import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {KeyboardAvoidingView, Platform, Text, View} from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _get from 'lodash/get';
import Header from './Header';
import Footer from './Footer';
import Question from './Question';
import styles from './GroupGamePlay.style';
import useGameWaitingTimer from 'shared/game/hooks/useGameWaitingTimer';
import { useOptimizedGroupPlay } from '../../hooks/useOptimizedGroupPlay';
import OptimizedLeaderboard from './OptimizedLeaderboard/OptimizedLeaderboard';
import OptimizedPointGained from './OptimizedPointGained/OptimizedPointGained';
import dark from '@/src/core/constants/themes/dark';
import WaitingPhaseTimer from './WaitingPhaseTimer';
import Dark from "core/constants/themes/dark";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import PerformanceMonitor from '../../components/PerformanceMonitor';

const WAITING_TIME = 5000;

const GROUP_PLAY_GAME_PHASES = {
  QUESTION_PHASE: "QUESTION_PHASE",
  WAITING_PHASE: "WAITING_PHASE",
  INITIAL_WAITING_PHASE: "INITIAL_WAITING_PHASE"
}

const GroupGamePlay = ({game}) => {
  const {startTime, config} = game ?? {};
  const gameStartTime = new Date(startTime).getTime();

  // Use optimized hook instead of the old one
  const {
    currentQuestion,
    currentQuestionId,
    submitAnswer,
    handleForceQuestionSubmission,
    currentQuestionIndex,
    allQuestionsCount,
    timeLeft,
    currentPhase,
    timePerQuestion,
    formatTime,
    renderOptimizations,
    isConnected,
  } = useOptimizedGroupPlay();

  const {isMobile} = useMediaQuery();

  const {isReady, renderQuestionOverlay: renderWaitingTimeOverlay} = useGameWaitingTimer({game});

  // Memoize phase mapping for performance
  const mappedCurrentPhase = useMemo(() => {
    switch (currentPhase) {
      case 'QUESTION_PHASE':
        return GROUP_PLAY_GAME_PHASES.QUESTION_PHASE;
      case 'WAITING_PHASE':
        return GROUP_PLAY_GAME_PHASES.WAITING_PHASE;
      case 'INITIAL_WAITING_PHASE':
      default:
        return GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE;
    }
  }, [currentPhase]);

  // Use the mapped phase, but fall back to initial waiting if not ready
  const effectiveCurrentPhase = useMemo(() => {
    if (!isReady) {
      return GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE;
    }
    return mappedCurrentPhase;
  }, [isReady, mappedCurrentPhase]);

  // Track last answered question for display during waiting phase
  const [lastAnsweredQuestion, setLastAnsweredQuestion] = useState(null);

  // Update last answered question when transitioning to waiting phase
  useEffect(() => {
    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE && currentQuestion) {
      setLastAnsweredQuestion(currentQuestion);
    }
  }, [effectiveCurrentPhase, currentQuestion]);

  const renderQuestionOverlay = useCallback(() => {
    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE) {
      return renderWaitingTimeOverlay?.();
    }

    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE) {
      const questionToShowAnswer = lastAnsweredQuestion || currentQuestion;
      return (
        <View style={[styles.overlay]}>
          {!renderOptimizations.skipAnimations && <WaitingPhaseTimer />}
          <Text style={{fontFamily: "Montserrat-600", fontSize: 14, color: dark.colors.textDark, textAlign: "center"}}>
            The Answer Was <Text style={{color: dark.colors.secondary}}>{questionToShowAnswer?.answers?.[0]}</Text>
          </Text>
          <PointGainedInCurrRoundInfo currentQuestionId={currentQuestionId}/>
        </View>
      );
    }

    return null;
  }, [effectiveCurrentPhase, renderWaitingTimeOverlay, currentQuestionId, currentQuestion, lastAnsweredQuestion, renderOptimizations.skipAnimations]);

  const renderTimerSection = useCallback(() => {
    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE) {
      return (<View style={{flexDirection: 'row', gap: 4}}>
        <MaterialIcons name={'timer'} color={Dark.colors.textDark} size={20}/>
        <Text style={[styles.timerText]}>
          {formatTime(timeLeft)}
        </Text>
      </View>)
    }
    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE) {
      return !renderOptimizations.skipAnimations ? <WaitingPhaseTimer/> : null;
    }
  }, [effectiveCurrentPhase, timeLeft, formatTime, renderOptimizations.skipAnimations])

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer
          question={currentQuestion}
          submitAnswer={submitAnswer}
          isGameActive={effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE}
          startTime={gameStartTime}
          handleForceQuestionSubmission={handleForceQuestionSubmission}
          timePerQuestion={timePerQuestion}
        />
      </View>
    ),
    [
      currentQuestion,
      submitAnswer,
      effectiveCurrentPhase,
      gameStartTime,
      handleForceQuestionSubmission,
      timePerQuestion,
    ],
  );

  // Connection status indicator
  const renderConnectionStatus = useCallback(() => {
    if (!isConnected) {
      return (
        <View style={{
          position: 'absolute',
          top: 10,
          right: 10,
          backgroundColor: 'rgba(255, 0, 0, 0.8)',
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 4,
          zIndex: 1000,
        }}>
          <Text style={{color: 'white', fontSize: 12, fontFamily: 'Montserrat-500'}}>
            Reconnecting...
          </Text>
        </View>
      );
    }
    return null;
  }, [isConnected]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{flex: 1}}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}>
        {renderConnectionStatus()}
        {/* Performance monitor - only show in development */}
        {__DEV__ && <PerformanceMonitor visible={renderOptimizations.memoryMode === 'low'} />}
        <LeaderboardInBetweenGame
          currentQuestionId={currentQuestionId}
        />
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={styles.mobileHeader}>
            <Header currentQuestionId={currentQuestionId}/>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              maxWidth: 420,
              paddingHorizontal: 16,
            }}>
              <View style={{flex: 1, flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'}}>
                <Text style={{fontSize: 14, fontFamily: 'Montserrat-500', color: dark.colors.textDark}}>
                  {`${currentQuestionIndex + 1} / ${allQuestionsCount}`}
                </Text>
              </View>
              <View style={{flex: 1.5, flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                {renderTimerSection()}
              </View>
              <View style={{flex: 1, flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}/>
            </View>

          </View>

          <View style={[styles.question, {position: 'relative'}]}>
            <Question
              question={currentQuestion}
              isVisible={effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE}
              renderQuestionOverlay={renderQuestionOverlay}
            />
          </View>

          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
};

export default React.memo(GroupGamePlay);