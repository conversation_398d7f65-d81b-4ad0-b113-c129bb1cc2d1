import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { KeyboardAvoidingView, Platform, Text, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _get from 'lodash/get';
import useGameWaitingTimer from 'shared/game/hooks/useGameWaitingTimer';
import dark from '@/src/core/constants/themes/dark';
import Dark from 'core/constants/themes/dark';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Header from './Header';
import Footer from './Footer';
import Question from './Question';
import styles from './GroupGamePlay.style';
import { useOptimizedGroupPlay } from '../../hooks/useOptimizedGroupPlay';
import useGroupPlayQuestionState from '../../hooks/useGroupPlayQuestionState';
import OptimizedLeaderboard from './OptimizedLeaderboard/OptimizedLeaderboard';
import OptimizedPointGained from './OptimizedPointGained/OptimizedPointGained';
import WaitingPhaseTimer from './WaitingPhaseTimer';
import PerformanceMonitor from '../../components/PerformanceMonitor';
import GameErrorBoundary from '../../components/GameErrorBoundary';

const WAITING_TIME = 5000;
const EMPTY_OBJECT = {};

const GROUP_PLAY_GAME_PHASES = {
  QUESTION_PHASE: 'QUESTION_PHASE',
  WAITING_PHASE: 'WAITING_PHASE',
  INITIAL_WAITING_PHASE: 'INITIAL_WAITING_PHASE',
};

const GroupGamePlay = ({ game }) => {
  const { startTime, config } = game ?? EMPTY_OBJECT;
  const gameStartTime = new Date(startTime).getTime();

  // Try optimized hook first, fallback to original if not ready
  const optimizedHookData = useOptimizedGroupPlay();
  const fallbackHookData = useGroupPlayQuestionState();

  // Use optimized hook if game is initialized, otherwise fallback
  const useOptimized =
    optimizedHookData.currentQuestion !== null &&
    optimizedHookData.currentQuestionId !== null;

  // Debug logging
  useEffect(() => {
    console.log('GroupGamePlay Debug:', {
      useOptimized,
      optimizedCurrentQuestion: optimizedHookData.currentQuestion,
      optimizedCurrentQuestionId: optimizedHookData.currentQuestionId,
      fallbackCurrentQuestion: fallbackHookData.currentQuestion,
      fallbackCurrentQuestionId: fallbackHookData.currentQuestionId,
      gameId: game?._id,
      questionsCount: game?.questions?.length,
    });
  }, [
    useOptimized,
    optimizedHookData.currentQuestion,
    fallbackHookData.currentQuestion,
    game,
  ]);

  const {
    currentQuestion,
    currentQuestionId,
    submitAnswer,
    handleForceQuestionSubmission,
    currentQuestionIndex,
    allQuestionsCount,
    timeLeft,
    currentPhase,
    timePerQuestion,
    formatTime,
    renderOptimizations,
    isConnected,
  } = useOptimized
    ? optimizedHookData
    : {
        ...fallbackHookData,
        // Add missing properties for fallback
        timeLeft: 0, // Will be calculated below for fallback
        currentPhase: 'QUESTION_PHASE', // Default phase
        formatTime: (ms) => Math.ceil(ms / 1000).toString(),
        renderOptimizations: {
          skipAnimations: false,
          reducedUpdates: false,
          memoryMode: 'normal',
        },
        isConnected: true, // Assume connected for fallback
      };

  // Fallback timing calculations when using original hook
  const timePerQuestionMs = useMemo(
    () => _get(config, 'maxTimePerQuestion', 10) * 1000,
    [config],
  );

  const calculateTimeRemainingInQuestion = useCallback(() => {
    if (!startTime || useOptimized) return timeLeft; // Use optimized value if available

    const currentTime = Date.now();
    const gameStartTime = new Date(startTime).getTime();

    if (currentTime < gameStartTime) {
      return timePerQuestionMs;
    }

    const cycleTime = timePerQuestionMs + WAITING_TIME;
    const currentCycle = Math.floor((currentTime - gameStartTime) / cycleTime);
    const currentQuestionStartTime = gameStartTime + currentCycle * cycleTime;
    const timeIntoCurrentPhase = currentTime - currentQuestionStartTime;

    if (timeIntoCurrentPhase < timePerQuestionMs) {
      return timePerQuestionMs - timeIntoCurrentPhase;
    }

    return 0;
  }, [startTime, timePerQuestionMs, useOptimized, timeLeft]);

  // Use calculated time for fallback
  const [fallbackTimeLeft, setFallbackTimeLeft] = useState(0);

  // Update fallback timer
  useEffect(() => {
    if (useOptimized) return; // Don't run timer if using optimized hook

    const timer = setInterval(() => {
      setFallbackTimeLeft(calculateTimeRemainingInQuestion());
    }, 100);

    return () => clearInterval(timer);
  }, [useOptimized, calculateTimeRemainingInQuestion]);

  const effectiveTimeLeft = useOptimized ? timeLeft : fallbackTimeLeft;

  const { isMobile } = useMediaQuery();

  const { isReady, renderQuestionOverlay: renderWaitingTimeOverlay } =
    useGameWaitingTimer({ game });

  // Memoize phase mapping for performance
  const mappedCurrentPhase = useMemo(() => {
    switch (currentPhase) {
      case 'QUESTION_PHASE':
        return GROUP_PLAY_GAME_PHASES.QUESTION_PHASE;
      case 'WAITING_PHASE':
        return GROUP_PLAY_GAME_PHASES.WAITING_PHASE;
      case 'INITIAL_WAITING_PHASE':
      default:
        return GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE;
    }
  }, [currentPhase]);

  // Use the mapped phase, but fall back to initial waiting if not ready
  const effectiveCurrentPhase = useMemo(() => {
    if (!isReady) {
      return GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE;
    }
    return mappedCurrentPhase;
  }, [isReady, mappedCurrentPhase]);

  // Track last answered question for display during waiting phase
  const [lastAnsweredQuestion, setLastAnsweredQuestion] = useState(null);

  // Update last answered question when transitioning to waiting phase
  useEffect(() => {
    if (
      effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE &&
      currentQuestion
    ) {
      setLastAnsweredQuestion(currentQuestion);
    }
  }, [effectiveCurrentPhase, currentQuestion]);

  const renderQuestionOverlay = useCallback(() => {
    if (
      effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE
    ) {
      return renderWaitingTimeOverlay?.();
    }

    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE) {
      const questionToShowAnswer = lastAnsweredQuestion || currentQuestion;
      return (
        <View style={[styles.overlay]}>
          {!renderOptimizations.skipAnimations && <WaitingPhaseTimer />}
          <Text
            style={{
              fontFamily: 'Montserrat-600',
              fontSize: 14,
              color: dark.colors.textDark,
              textAlign: 'center',
            }}
          >
            The Answer Was{' '}
            <Text style={{ color: dark.colors.secondary }}>
              {questionToShowAnswer?.answers?.[0]}
            </Text>
          </Text>
          <OptimizedPointGained currentQuestionId={currentQuestionId} />
        </View>
      );
    }

    return null;
  }, [
    effectiveCurrentPhase,
    renderWaitingTimeOverlay,
    currentQuestionId,
    currentQuestion,
    lastAnsweredQuestion,
    renderOptimizations.skipAnimations,
  ]);

  const renderTimerSection = useCallback(() => {
    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE) {
      return (
        <View style={{ flexDirection: 'row', gap: 4 }}>
          <MaterialIcons name="timer" color={Dark.colors.textDark} size={20} />
          <Text style={[styles.timerText]}>
            {formatTime(effectiveTimeLeft)}
          </Text>
        </View>
      );
    }
    if (effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE) {
      return !renderOptimizations.skipAnimations ? <WaitingPhaseTimer /> : null;
    }
  }, [
    effectiveCurrentPhase,
    effectiveTimeLeft,
    formatTime,
    renderOptimizations.skipAnimations,
  ]);

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer
          question={currentQuestion}
          submitAnswer={submitAnswer}
          isGameActive={
            effectiveCurrentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE
          }
          startTime={gameStartTime}
          handleForceQuestionSubmission={handleForceQuestionSubmission}
          timePerQuestion={timePerQuestion}
        />
      </View>
    ),
    [
      currentQuestion,
      submitAnswer,
      effectiveCurrentPhase,
      gameStartTime,
      handleForceQuestionSubmission,
      timePerQuestion,
    ],
  );

  // Connection status indicator
  const renderConnectionStatus = useCallback(() => {
    if (!isConnected) {
      return (
        <View
          style={{
            position: 'absolute',
            top: 10,
            right: 10,
            backgroundColor: 'rgba(255, 0, 0, 0.8)',
            paddingHorizontal: 8,
            paddingVertical: 4,
            borderRadius: 4,
            zIndex: 1000,
          }}
        >
          <Text
            style={{
              color: 'white',
              fontSize: 12,
              fontFamily: 'Montserrat-500',
            }}
          >
            Reconnecting...
          </Text>
        </View>
      );
    }
    return null;
  }, [isConnected]);

  return (
    <GameErrorBoundary>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
      >
        <View
          style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
        >
          {renderConnectionStatus()}
          {/* Performance monitor - only show in development */}
          <PerformanceMonitor
            visible={renderOptimizations.memoryMode === 'low'}
          />
          <OptimizedLeaderboard currentQuestionId={currentQuestionId} />
          {/* Debug info */}
          {__DEV__ && (
            <View
              style={{
                position: 'absolute',
                bottom: 10,
                left: 10,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                padding: 8,
                borderRadius: 4,
                zIndex: 1000,
              }}
            >
              <Text style={{ color: 'white', fontSize: 10 }}>
                Phase: {effectiveCurrentPhase}
              </Text>
              <Text style={{ color: 'white', fontSize: 10 }}>
                Time: {Math.ceil(effectiveTimeLeft / 1000)}s
              </Text>
              <Text style={{ color: 'white', fontSize: 10 }}>
                Question: {currentQuestionIndex + 1}/{allQuestionsCount}
              </Text>
              <Text style={{ color: 'white', fontSize: 10 }}>
                Using: {useOptimized ? 'Optimized' : 'Fallback'}
              </Text>
            </View>
          )}
          <View style={[styles.container, !isMobile && styles.webContainer]}>
            <View style={styles.mobileHeader}>
              <Header currentQuestionId={currentQuestionId} />
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%',
                  maxWidth: 420,
                  paddingHorizontal: 16,
                }}
              >
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                  }}
                >
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: 'Montserrat-500',
                      color: dark.colors.textDark,
                    }}
                  >
                    {`${currentQuestionIndex + 1} / ${allQuestionsCount}`}
                  </Text>
                </View>
                <View
                  style={{
                    flex: 1.5,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  {renderTimerSection()}
                </View>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                />
              </View>
            </View>

            <View style={[styles.question, { position: 'relative' }]}>
              {currentQuestion ? (
                <Question
                  question={currentQuestion}
                  isVisible={
                    effectiveCurrentPhase ===
                    GROUP_PLAY_GAME_PHASES.QUESTION_PHASE
                  }
                  renderQuestionOverlay={renderQuestionOverlay}
                />
              ) : (
                <View style={[styles.overlay]}>
                  <Text
                    style={{
                      fontFamily: 'Montserrat-600',
                      fontSize: 16,
                      color: dark.colors.textDark,
                      textAlign: 'center',
                    }}
                  >
                    Loading question...
                  </Text>
                </View>
              )}
            </View>

            {!isMobile && renderFooter()}
          </View>
          {isMobile && renderFooter()}
        </View>
      </KeyboardAvoidingView>
    </GameErrorBoundary>
  );
};

export default React.memo(GroupGamePlay);
