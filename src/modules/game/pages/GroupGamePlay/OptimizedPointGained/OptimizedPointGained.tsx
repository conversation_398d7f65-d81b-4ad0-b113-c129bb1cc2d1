import React, { useCallback, useMemo } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useRouter } from 'expo-router';
import useMultiplayerGameStore from '@/src/store/useMultiplayerGameStore';
import useGameContext from '../../../hooks/useGameContext';
import useGroupPlayUserScore from '../../../hooks/useGroupPlayUserScore';
import { processGroupPlayPlayersData } from '@/src/modules/game/utils/processGroupPlayPlayersData';
import useMediaQuery from 'core/hooks/useMediaQuery';
import UserImage from 'atoms/UserImage';
import userReader from 'core/readers/userReader';
import numeral from 'numeral';
import dark from '@/src/core/constants/themes/dark';
import _filter from 'lodash/filter';

const NUMERAL_FORMAT = '0[.]00a';

interface OptimizedPointGainedProps {
  currentQuestionId: string | null;
}

const OptimizedPointGained: React.FC<OptimizedPointGainedProps> = ({
  currentQuestionId,
}) => {
  const { userId } = useSession();
  const router = useRouter();
  const { isMobile } = useMediaQuery();

  const { playerScores, timing, renderOptimizations } = useMultiplayerGameStore(
    (state) => ({
      playerScores: state.playerScores,
      timing: state.timing,
      renderOptimizations: state.renderOptimizations,
    }),
  );

  // Get players who scored in the current round
  const currentRoundScorers = useMemo(() => {
    if (!currentQuestionId || timing.currentPhase !== 'WAITING_PHASE') {
      return [];
    }

    return Object.values(playerScores)
      .filter((player) => player.currentQuestionScore > 0)
      .sort((a, b) => b.currentQuestionScore - a.currentQuestionScore)
      .slice(0, renderOptimizations.memoryMode === 'low' ? 5 : 10); // Limit for performance
  }, [
    playerScores,
    currentQuestionId,
    timing.currentPhase,
    renderOptimizations.memoryMode,
  ]);

  const navigateToProfile = useCallback(
    (userId: string) => {
      if (renderOptimizations.reducedUpdates) {
        // Debounce navigation on low-end devices
        setTimeout(() => {
          router.push(`/profile/${userId}`);
        }, 100);
      } else {
        router.push(`/profile/${userId}`);
      }
    },
    [router, renderOptimizations.reducedUpdates],
  );

  const renderPlayerRow = useCallback(
    (player: any, index: number) => {
      const isCurrentUser = player.userId === userId;
      const pointsGained = player.currentQuestionScore;

      if (pointsGained <= 0) return null;

      return (
        <TouchableOpacity
          key={player.userId}
          onPress={() => navigateToProfile(player.userId)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 8,
            paddingHorizontal: 12,
            backgroundColor: isCurrentUser
              ? `${dark.colors.primary}20`
              : 'transparent',
            borderRadius: 8,
            marginVertical: 2,
          }}
          disabled={renderOptimizations.reducedUpdates} // Disable interactions on low-end devices
        >
          <View style={{ marginRight: 12 }}>
            <UserImage user={player} size={32} style={{ borderRadius: 16 }} />
          </View>

          <View style={{ flex: 1 }}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: 'Montserrat-600',
                color: dark.colors.textDark,
              }}
              numberOfLines={1}
            >
              {userReader.displayName(player)}
            </Text>
            <Text
              style={{
                fontSize: 12,
                fontFamily: 'Montserrat-400',
                color: dark.colors.textLight,
              }}
            >
              Rank #{index + 1}
            </Text>
          </View>

          <View style={{ alignItems: 'flex-end' }}>
            <Text
              style={{
                fontSize: 16,
                fontFamily: 'Montserrat-700',
                color: dark.colors.secondary,
              }}
            >
              +{numeral(pointsGained).format(NUMERAL_FORMAT)}
            </Text>
            <Text
              style={{
                fontSize: 10,
                fontFamily: 'Montserrat-400',
                color: dark.colors.textLight,
              }}
            >
              points
            </Text>
          </View>
        </TouchableOpacity>
      );
    },
    [userId, navigateToProfile, renderOptimizations.reducedUpdates],
  );

  // Don't render if no scorers or not in waiting phase
  if (
    currentRoundScorers.length === 0 ||
    timing.currentPhase !== 'WAITING_PHASE'
  ) {
    return null;
  }

  return (
    <View
      style={{
        backgroundColor: dark.colors.background,
        borderRadius: 12,
        padding: 16,
        marginVertical: 8,
        marginHorizontal: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}
    >
      <Text
        style={{
          fontSize: 16,
          fontFamily: 'Montserrat-700',
          color: dark.colors.textDark,
          marginBottom: 12,
          textAlign: 'center',
        }}
      >
        Round {timing.currentQuestionIndex + 1} Results
      </Text>

      {renderOptimizations.memoryMode === 'low' ? (
        // Simple list for low-end devices
        <View>
          {currentRoundScorers.map((player, index) =>
            renderPlayerRow(player, index),
          )}
        </View>
      ) : (
        // Scrollable list for normal/high-end devices
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ maxHeight: 200 }}
          removeClippedSubviews={renderOptimizations.reducedUpdates}
        >
          {currentRoundScorers.map((player, index) =>
            renderPlayerRow(player, index),
          )}
        </ScrollView>
      )}

      {currentRoundScorers.length === 0 && (
        <Text
          style={{
            fontSize: 14,
            fontFamily: 'Montserrat-500',
            color: dark.colors.textLight,
            textAlign: 'center',
            fontStyle: 'italic',
          }}
        >
          No one scored this round
        </Text>
      )}
    </View>
  );
};

export default React.memo(OptimizedPointGained);
