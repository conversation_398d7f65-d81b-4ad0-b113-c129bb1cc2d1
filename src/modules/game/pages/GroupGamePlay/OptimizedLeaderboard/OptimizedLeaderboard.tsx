import React, { useCallback, useMemo } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useMultiplayerGameStore from '@/src/store/useMultiplayerGameStore';
import { processGroupPlayPlayersData } from '@/src/modules/game/utils/processGroupPlayPlayersData';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';
import _size from 'lodash/size';
import _map from 'lodash/map';
import GroupPlayLeaderboardRow from '../../GroupPlayResult/components/GroupPlayLeaderboardRow';
import styles from '../../GroupPlayResult/GroupPlayResult.style';
import useGroupPlayUserScore from '../../../hooks/useGroupPlayUserScore';
import useGameContext from '../../../hooks/useGameContext';

interface OptimizedLeaderboardProps {
  currentQuestionId: string | null;
}

const OptimizedLeaderboard: React.FC<OptimizedLeaderboardProps> = ({
  currentQuestionId,
}) => {
  const { userId } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();

  // Get data from optimized store
  const {
    playerScores,
    leaderboardVisible,
    renderOptimizations,
    timing,
    gameId,
  } = useMultiplayerGameStore((state) => ({
    playerScores: state.playerScores,
    leaderboardVisible: state.leaderboardVisible,
    renderOptimizations: state.renderOptimizations,
    timing: state.timing,
    gameId: state.gameId,
  }));

  // Get fallback data from original context
  const { players, game } = useGameContext();
  const { getUserCurrentQuestionScore } = useGroupPlayUserScore({ game });

  // Determine if we should use optimized store or fallback
  const useOptimizedStore = gameId && Object.keys(playerScores).length > 0;

  // Debug logging

  // Get players data - either from optimized store or fallback
  const playersData = useMemo(() => {
    if (useOptimizedStore) {
      return Object.values(playerScores);
    }
    // Fallback to original logic
    const { rankedPlayers } =
      processGroupPlayPlayersData({ players, game }) ?? EMPTY_OBJECT;
    return rankedPlayers || [];
  }, [useOptimizedStore, playerScores, players, game]);

  React.useEffect(() => {
    console.info('OptimizedLeaderboard Debug:', {
      useOptimizedStore,
      gameId,
      playerScoresCount: Object.keys(playerScores).length,
      leaderboardVisible,
      currentPhase: timing?.currentPhase,
      playersDataLength: playersData.length,
      isCompactMode,
      shouldShow: !isCompactMode && playersData.length > 0,
    });
  }, [
    useOptimizedStore,
    gameId,
    playerScores,
    leaderboardVisible,
    timing?.currentPhase,
    playersData.length,
    isCompactMode,
  ]);

  // Memoized sorted players list
  const rankedPlayers = useMemo(() => {
    if (useOptimizedStore) {
      // Sort optimized store data
      return playersData.sort((a: any, b: any) => {
        if (b.totalScore !== a.totalScore) {
          return b.totalScore - a.totalScore;
        }
        return a.lastUpdated - b.lastUpdated; // Earlier submission wins
      });
    }
    // Fallback data is already sorted
    return playersData;
  }, [useOptimizedStore, playersData]);

  // Limit displayed players for performance on low-end devices
  const displayedPlayers = useMemo(() => {
    const memoryMode = renderOptimizations?.memoryMode || 'normal';

    if (memoryMode === 'low') {
      return rankedPlayers.slice(0, 10); // Show only top 10
    }
    if (memoryMode === 'normal') {
      return rankedPlayers.slice(0, 20); // Show top 20
    }
    return rankedPlayers; // Show all
  }, [rankedPlayers, renderOptimizations]);

  // Render individual leaderboard row
  const renderLeaderboardRow = useCallback(
    (player: any, index: number) => {
      if (useOptimizedStore) {
        // Use optimized store data
        const isCurrentUser = player.userId === userId;
        const score =
          timing?.currentPhase === 'QUESTION_PHASE'
            ? player.currentQuestionScore
            : player.totalScore;

        return (
          <View key={player.userId}>
            <GroupPlayLeaderboardRow
              player={{
                ...player,
                score,
                rank: index + 1,
              }}
              isCurrentUser={isCurrentUser}
              isCompactMode={isCompactMode}
              showAnimation={!renderOptimizations?.skipAnimations}
            />
          </View>
        );
      }
      // Use fallback logic (original implementation)
      const { hasSolved, score } = getUserCurrentQuestionScore({
        playerId: player?._id,
        questionId: currentQuestionId,
      });

      return (
        <View
          key={`${index}`}
          style={[
            {
              paddingVertical: 10,
              paddingHorizontal: 16,
              borderBottomColor: dark.colors.tertiary,
              borderBottomWidth: 1,
            },
            hasSolved && {
              backgroundColor: '#263624',
            },
          ]}
        >
          <GroupPlayLeaderboardRow
            score={player?.score}
            currentQuesScore={hasSolved ? score : undefined}
            user={player}
            rank={index + 1}
          />
        </View>
      );
    },
    [
      useOptimizedStore,
      userId,
      timing,
      renderOptimizations,
      isCompactMode,
      getUserCurrentQuestionScore,
      currentQuestionId,
    ],
  );

  // Key extractor for FlatList
  const keyExtractor = useCallback((item: any) => item.userId || item._id, []);

  // Hide on mobile (like original implementation)
  if (isCompactMode) {
    return null;
  }

  // Don't render if no players data
  if (displayedPlayers.length === 0) {
    return null;
  }

  // Render leaderboard (similar to original layout)
  return (
    <View
      style={{
        height: '100%',
        width: 300,
        backgroundColor: dark.colors.background,
        flex: 1,
      }}
    >
      <View
        style={{
          flex: 1,
          backgroundColor: dark.colors.gradientBackground,
          borderRadius: 12,
          marginLeft: 16,
          marginVertical: 16,
        }}
      >
        {_size(displayedPlayers) > 0 && (
          <View
            style={{
              flexDirection: 'row',
              marginTop: 15,
              paddingHorizontal: 16,
            }}
          >
            <Text
              style={[
                styles.rowHeadingText,
                styles.rankHeader,
                !isCompactMode && { flex: 0.2 },
              ]}
            >
              #
            </Text>
            <Text style={[styles.rowHeadingText, styles.userInfo]}>
              Mathlete
            </Text>
            <Text
              style={[
                styles.rowHeadingText,
                { textAlign: 'right' },
                styles.scoreDetail,
              ]}
            >
              Score
            </Text>
          </View>
        )}
        <ScrollView showsVerticalScrollIndicator={false}>
          {_map(displayedPlayers, renderLeaderboardRow)}
        </ScrollView>
      </View>
    </View>
  );
};

export default React.memo(OptimizedLeaderboard);
