import React, { useMemo, useCallback } from 'react';
import { ScrollView, Text, View, FlatList } from 'react-native';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useMultiplayerGameStore from '@/src/store/useMultiplayerGameStore';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';
import GroupPlayLeaderboardRow from '../../GroupPlayResult/components/GroupPlayLeaderboardRow';
import styles from '../../GroupPlayResult/GroupPlayResult.style';

const EMPTY_ARRAY: any[] = [];

interface OptimizedLeaderboardProps {
  currentQuestionId: string | null;
}

const OptimizedLeaderboard: React.FC<OptimizedLeaderboardProps> = ({ 
  currentQuestionId 
}) => {
  const { userId } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();

  const { 
    playerScores, 
    leaderboardVisible, 
    renderOptimizations,
    timing 
  } = useMultiplayerGameStore((state) => ({
    playerScores: state.playerScores,
    leaderboardVisible: state.leaderboardVisible,
    renderOptimizations: state.renderOptimizations,
    timing: state.timing,
  }));

  // Memoized sorted players list
  const rankedPlayers = useMemo(() => {
    const players = Object.values(playerScores);
    
    // Sort by total score, then by last updated time for tie-breaking
    return players.sort((a, b) => {
      if (b.totalScore !== a.totalScore) {
        return b.totalScore - a.totalScore;
      }
      return a.lastUpdated - b.lastUpdated; // Earlier submission wins
    });
  }, [playerScores]);

  // Limit displayed players for performance on low-end devices
  const displayedPlayers = useMemo(() => {
    if (renderOptimizations.memoryMode === 'low') {
      return rankedPlayers.slice(0, 10); // Show only top 10
    }
    if (renderOptimizations.memoryMode === 'normal') {
      return rankedPlayers.slice(0, 20); // Show top 20
    }
    return rankedPlayers; // Show all
  }, [rankedPlayers, renderOptimizations.memoryMode]);

  // Get current user's score for the current question
  const getCurrentQuestionScore = useCallback((userId: string) => {
    const player = playerScores[userId];
    if (!player) return 0;
    
    // During question phase, show current question score
    // During waiting phase, show total score
    if (timing.currentPhase === 'QUESTION_PHASE') {
      return player.currentQuestionScore;
    }
    return player.totalScore;
  }, [playerScores, timing.currentPhase]);

  // Render individual leaderboard row
  const renderLeaderboardRow = useCallback(({ item: player, index }: { item: any; index: number }) => {
    const isCurrentUser = player.userId === userId;
    const score = getCurrentQuestionScore(player.userId);
    
    return (
      <GroupPlayLeaderboardRow
        key={player.userId}
        player={{
          ...player,
          score,
          rank: index + 1,
        }}
        isCurrentUser={isCurrentUser}
        isCompactMode={isCompactMode}
        showAnimation={!renderOptimizations.skipAnimations}
      />
    );
  }, [userId, getCurrentQuestionScore, isCompactMode, renderOptimizations.skipAnimations]);

  // Key extractor for FlatList
  const keyExtractor = useCallback((item: any) => item.userId, []);

  // Don't render if leaderboard is not visible
  if (!leaderboardVisible || displayedPlayers.length === 0) {
    return null;
  }

  // For low-end devices, use FlatList for better performance
  if (renderOptimizations.memoryMode === 'low') {
    return (
      <View style={[styles.leaderboardContainer, { maxHeight: 300 }]}>
        <Text style={styles.leaderboardTitle}>Current Standings</Text>
        <FlatList
          data={displayedPlayers}
          renderItem={renderLeaderboardRow}
          keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={5}
          windowSize={10}
          initialNumToRender={5}
          getItemLayout={(data, index) => ({
            length: 60, // Approximate row height
            offset: 60 * index,
            index,
          })}
        />
      </View>
    );
  }

  // For normal/high-end devices, use ScrollView
  return (
    <View style={styles.leaderboardContainer}>
      <Text style={styles.leaderboardTitle}>Current Standings</Text>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        style={{ maxHeight: 400 }}
        removeClippedSubviews={renderOptimizations.reducedUpdates}
      >
        {displayedPlayers.map((player, index) => (
          <View key={player.userId}>
            {renderLeaderboardRow({ item: player, index })}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default React.memo(OptimizedLeaderboard);
