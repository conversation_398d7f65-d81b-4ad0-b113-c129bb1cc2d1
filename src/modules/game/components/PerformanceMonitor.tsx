import React, { useEffect, useRef, useState } from 'react';
import { View, Text } from 'react-native';
import useMultiplayerGameStore from '@/src/store/useMultiplayerGameStore';

interface PerformanceStats {
  fps: number;
  renderTime: number;
  memoryUsage: number;
  eventQueueSize: number;
}

const PerformanceMonitor: React.FC<{ visible?: boolean }> = ({ visible = false }) => {
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 60,
    renderTime: 0,
    memoryUsage: 0,
    eventQueueSize: 0,
  });

  const frameCount = useRef(0);
  const lastTime = useRef(Date.now());
  const renderTimes = useRef<number[]>([]);

  const { eventQueue, renderOptimizations } = useMultiplayerGameStore((state) => ({
    eventQueue: state.eventQueue,
    renderOptimizations: state.renderOptimizations,
  }));

  useEffect(() => {
    if (!visible) return;

    const measurePerformance = () => {
      const now = Date.now();
      const deltaTime = now - lastTime.current;
      
      frameCount.current++;
      
      // Calculate FPS every second
      if (deltaTime >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / deltaTime);
        frameCount.current = 0;
        lastTime.current = now;
        
        // Calculate average render time
        const avgRenderTime = renderTimes.current.length > 0
          ? renderTimes.current.reduce((sum, time) => sum + time, 0) / renderTimes.current.length
          : 0;
        
        // Get memory usage if available
        let memoryUsage = 0;
        if (typeof performance !== 'undefined' && (performance as any).memory) {
          memoryUsage = Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024);
        }
        
        setStats({
          fps,
          renderTime: Math.round(avgRenderTime * 100) / 100,
          memoryUsage,
          eventQueueSize: eventQueue.length,
        });
        
        // Clear render times for next measurement
        renderTimes.current = [];
      }
      
      requestAnimationFrame(measurePerformance);
    };

    const animationId = requestAnimationFrame(measurePerformance);
    
    return () => {
      cancelAnimationFrame(animationId);
    };
  }, [visible, eventQueue.length]);

  // Track render times
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (renderTimes.current.length < 60) {
        renderTimes.current.push(renderTime);
      } else {
        renderTimes.current.shift();
        renderTimes.current.push(renderTime);
      }
    };
  });

  if (!visible) return null;

  const getPerformanceColor = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return '#4CAF50'; // Green
    if (value <= thresholds.poor) return '#FF9800'; // Orange
    return '#F44336'; // Red
  };

  const getFpsColor = (fps: number) => getPerformanceColor(60 - fps, { good: 10, poor: 20 });
  const getRenderTimeColor = (time: number) => getPerformanceColor(time, { good: 16.67, poor: 33.33 });
  const getMemoryColor = (memory: number) => getPerformanceColor(memory, { good: 50, poor: 100 });
  const getQueueColor = (size: number) => getPerformanceColor(size, { good: 5, poor: 20 });

  return (
    <View style={{
      position: 'absolute',
      top: 50,
      left: 10,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      padding: 8,
      borderRadius: 4,
      zIndex: 1000,
      minWidth: 120,
    }}>
      <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold', marginBottom: 4 }}>
        Performance Monitor
      </Text>
      
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 2 }}>
        <Text style={{ color: 'white', fontSize: 9 }}>FPS:</Text>
        <Text style={{ color: getFpsColor(stats.fps), fontSize: 9, fontWeight: 'bold' }}>
          {stats.fps}
        </Text>
      </View>
      
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 2 }}>
        <Text style={{ color: 'white', fontSize: 9 }}>Render:</Text>
        <Text style={{ color: getRenderTimeColor(stats.renderTime), fontSize: 9, fontWeight: 'bold' }}>
          {stats.renderTime}ms
        </Text>
      </View>
      
      {stats.memoryUsage > 0 && (
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 2 }}>
          <Text style={{ color: 'white', fontSize: 9 }}>Memory:</Text>
          <Text style={{ color: getMemoryColor(stats.memoryUsage), fontSize: 9, fontWeight: 'bold' }}>
            {stats.memoryUsage}MB
          </Text>
        </View>
      )}
      
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 2 }}>
        <Text style={{ color: 'white', fontSize: 9 }}>Queue:</Text>
        <Text style={{ color: getQueueColor(stats.eventQueueSize), fontSize: 9, fontWeight: 'bold' }}>
          {stats.eventQueueSize}
        </Text>
      </View>
      
      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <Text style={{ color: 'white', fontSize: 9 }}>Mode:</Text>
        <Text style={{ 
          color: renderOptimizations.memoryMode === 'low' ? '#FF9800' : '#4CAF50', 
          fontSize: 9, 
          fontWeight: 'bold' 
        }}>
          {renderOptimizations.memoryMode}
        </Text>
      </View>
    </View>
  );
};

export default React.memo(PerformanceMonitor);
