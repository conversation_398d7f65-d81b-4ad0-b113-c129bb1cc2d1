import React, { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';
import _toNumber from 'lodash/toNumber';
import PropTypes from 'prop-types';
import dark from 'core/constants/themes/dark';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import styles from './GameStartTimer.style';
// import GameLobbyPlayerCards from '../GameLobbyPlayerCards/GameLobbyPlayerCards'

const GameStartTimer = (props) => {
  const { startTime } = props;
  const startTimeDate = new Date(startTime);
  const timeDiff = startTimeDate.getTime() - getCurrentTime();
  const [timer, setTimer] = useState(Math.ceil(_toNumber(timeDiff) / 1000));

  const currTimeRef = useRef();

  useEffect(() => {
    if (currTimeRef.current) {
      clearInterval(currTimeRef.current);
    }

    currTimeRef.current = setInterval(() => {
      const timeLeft = startTimeDate.getTime() - getCurrentTimeWithOffset();
      if (timeLeft <= 0) {
        setTimer(0);
        clearInterval(currTimeRef.current);
        return;
      }
      setTimer(Math.ceil(timeLeft / 1000));
    }, 100);

    return () => clearInterval(currTimeRef.current);
  }, []);

  return (
    <View
      style={{
        padding: 0,
        margin: 0,
        flex: 1,
        backgroundColor: dark.colors.background,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Text style={styles.timerText}>
        Starting in <Text style={styles.time}>{timer}</Text>
      </Text>
    </View>
  );
};

GameStartTimer.propTypes = {
  isVisible: PropTypes.bool,
  startTime: PropTypes.number,
};

export default React.memo(GameStartTimer);
