import { useCallback, useEffect, useRef, useMemo } from 'react';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useMultiplayerGameStore from '@/src/store/useMultiplayerGameStore';
import useGameContext from './useGameContext';
import EventManager from 'core/event';
import { WEBSOCKET_CHANNELS } from 'core/services/WebSocketManager';
import useWebsocketStore from 'store/useWebSocketStore';
import { listenersNamespace } from 'core/event/constants';

const EMPTY_OBJECT = {};

export const useOptimizedGroupPlay = () => {
  const { userId } = useSession();
  const { game } = useGameContext();
  
  const {
    // State
    gameId,
    questions,
    currentQuestionId,
    timing,
    playerScores,
    leaderboardVisible,
    lastAnsweredQuestion,
    renderOptimizations,
    isConnected,
    
    // Actions
    initializeGame,
    addEvent,
    processBatchedEvents,
    updatePlayerScore,
    submitAnswer: storeSubmitAnswer,
    syncWithServer,
    clearGameState,
  } = useMultiplayerGameStore((state) => ({
    gameId: state.gameId,
    questions: state.questions,
    currentQuestionId: state.currentQuestionId,
    timing: state.timing,
    playerScores: state.playerScores,
    leaderboardVisible: state.leaderboardVisible,
    lastAnsweredQuestion: state.lastAnsweredQuestion,
    renderOptimizations: state.renderOptimizations,
    isConnected: state.isConnected,
    initializeGame: state.initializeGame,
    addEvent: state.addEvent,
    processBatchedEvents: state.processBatchedEvents,
    updatePlayerScore: state.updatePlayerScore,
    submitAnswer: state.submitAnswer,
    syncWithServer: state.syncWithServer,
    clearGameState: state.clearGameState,
  }));

  const { sendMessage, joinChannel, leaveChannel } = useWebsocketStore((state) => ({
    sendMessage: state.sendMessage,
    joinChannel: state.joinChannel,
    leaveChannel: state.leaveChannel,
  }));

  // Refs for stable references
  const addEventRef = useRef(addEvent);
  addEventRef.current = addEvent;

  const processBatchedEventsRef = useRef(processBatchedEvents);
  processBatchedEventsRef.current = processBatchedEvents;

  const syncWithServerRef = useRef(syncWithServer);
  syncWithServerRef.current = syncWithServer;

  // Initialize game when game data is available
  useEffect(() => {
    if (game && game._id && !gameId) {
      initializeGame(game);
    }
  }, [game, gameId, initializeGame]);

  // WebSocket event handling
  useEffect(() => {
    if (!game?._id) return;

    const channel = WEBSOCKET_CHANNELS.GameEvents(game._id);
    joinChannel(channel);

    const eventManager = new EventManager();
    const subscription = eventManager.on(
      game._id,
      listenersNamespace.GameEvent,
      (data: any) => {
        handleGameEvent(data);
      }
    );

    return () => {
      subscription.unsubscribe();
      leaveChannel(channel);
    };
  }, [game?._id, joinChannel, leaveChannel]);

  // Process events periodically
  useEffect(() => {
    const interval = setInterval(() => {
      processBatchedEventsRef.current();
    }, renderOptimizations.reducedUpdates ? 500 : 200);

    return () => clearInterval(interval);
  }, [renderOptimizations.reducedUpdates]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearGameState();
    };
  }, [clearGameState]);

  const handleGameEvent = useCallback((data: any) => {
    const { event, game: gameData, user, score } = data;
    const timestamp = Date.now();

    switch (event) {
      case 'SCORE_UPDATED':
        addEventRef.current({
          type: 'scoreUpdate',
          data: {
            userId: user?._id,
            username: user?.username,
            avatar: user?.avatar,
            score: score?.currentQuestionScore || 0,
            totalScore: score?.totalScore || 0,
            questionId: score?.questionId,
          },
          timestamp,
          priority: 7,
          userId: user?._id,
        });
        break;

      case 'PLAYER_JOINED':
        addEventRef.current({
          type: 'playerJoined',
          data: {
            userId: user?._id,
            username: user?.username,
            avatar: user?.avatar,
          },
          timestamp,
          priority: 5,
          userId: user?._id,
        });
        break;

      case 'PLAYER_LEFT':
        addEventRef.current({
          type: 'playerLeft',
          data: {
            userId: user?._id,
          },
          timestamp,
          priority: 5,
          userId: user?._id,
        });
        break;

      case 'GAME_STATE_SYNC':
        addEventRef.current({
          type: 'gameStateSync',
          data: {
            gameState: gameData,
          },
          timestamp,
          priority: 10,
        });
        break;

      default:
        // Handle other events
        addEventRef.current({
          type: event,
          data,
          timestamp,
          priority: 3,
        });
    }
  }, []);

  const submitAnswer = useCallback((questionId: string, answer: string) => {
    if (!game?._id) return;

    // Update local state immediately for responsiveness
    storeSubmitAnswer(questionId, answer);

    // Send to server
    const channel = WEBSOCKET_CHANNELS.GameEvents(game._id);
    sendMessage({
      type: 'submitAnswer',
      channel,
      data: {
        questionId,
        answer,
        timestamp: Date.now(),
      },
    });
  }, [game?._id, storeSubmitAnswer, sendMessage]);

  const handleForceQuestionSubmission = useCallback(() => {
    if (!currentQuestionId) return;
    
    const currentQuestion = questions[currentQuestionId];
    if (currentQuestion?.userAnswer) {
      submitAnswer(currentQuestionId, currentQuestion.userAnswer);
    }
  }, [currentQuestionId, questions, submitAnswer]);

  // Memoized derived data
  const currentQuestion = useMemo(() => {
    return currentQuestionId ? questions[currentQuestionId]?.question : null;
  }, [currentQuestionId, questions]);

  const currentQuestionIndex = useMemo(() => {
    return timing.currentQuestionIndex;
  }, [timing.currentQuestionIndex]);

  const allQuestionsCount = useMemo(() => {
    return Object.keys(questions).length;
  }, [questions]);

  const playersScores = useMemo(() => {
    const scores: Record<string, number> = {};
    Object.values(playerScores).forEach(player => {
      scores[player.userId] = player.totalScore;
    });
    return scores;
  }, [playerScores]);

  const formatTime = useCallback((ms: number) => {
    const seconds = Math.ceil(ms / 1000);
    return `${seconds}`;
  }, []);

  return {
    // Game state
    currentQuestion,
    currentQuestionId,
    currentQuestionIndex,
    allQuestionsCount,
    
    // Timing
    timeLeft: timing.timeRemaining,
    currentPhase: timing.currentPhase,
    gameStartTime: timing.gameStartTime,
    timePerQuestion: timing.timePerQuestion * 1000, // Convert to ms for compatibility
    
    // Leaderboard
    playersScores,
    leaderboardVisible,
    lastAnsweredQuestion,
    
    // Actions
    submitAnswer,
    handleForceQuestionSubmission,
    
    // Performance
    renderOptimizations,
    isConnected,
    
    // Utils
    formatTime,
    
    // Question state
    incorrectAttempts: questions[currentQuestionId || '']?.incorrectAttempts || 0,
    solvedAllQuestions: Object.values(questions).every(q => q.hasSolved),
  };
};
