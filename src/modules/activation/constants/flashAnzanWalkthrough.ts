import BlitzFooter from '../pages/ExploreBlitzGame/components/BlitzFooter';
import {
  MockConversion,
  MockFlashingSection,
  MockGameSettings,
  MockNextQuestionCountdownSection,
  MockPlayerInfo,
  MockPrepPhaseCountdown,
} from '../pages/ExploreFlashAnzanGame/components/MockFlashAnzanPhases';

export const WALKTHROUGH_PHASES = {
  CONFIG: 'config',
  FLASHING: 'flashing',
  ANSWERING: 'answering',
};

export const FLASH_ANZAN_CONFIG_STEPS = [
  {
    name: 'Scoreboard ',
    text: 'track ongoing scores after every round',
    order: 1,
    component: MockPlayerInfo,
  },
  {
    name: 'Preparation Phase',
    text: 'This shows the time remaining before the game countdown starts.',
    order: 2,
    component: MockPrepPhaseCountdown,
  },
  {
    name: 'Points Conversion',
    text: 'Points vary based on question difficulty',
    order: 3,
    component: MockConversion,
  },
  {
    name: 'Customise question',
    text: 'Customise the type of questions\n\nPoints depend on \ndigits, time, and integer type.',
    order: 4,
    component: MockGameSettings,
  },
];

export const FLASH_ANZAN_FLASHING_STEPS = [
  {
    name: 'Playzone',
    text: 'Add the number to get your final answer',
    order: 5,
    component: MockFlashingSection,
  },
];

export const FLASH_ANZAN_ANSWERING_STEPS = [
  {
    name: 'Answer Time Countdown',
    text: 'This shows the time remaining to answer the question.',
    order: 6,
    component: MockNextQuestionCountdownSection,
  },
];

export const FLASH_ANZAN_FOOTER_STEP = {
  name: 'Answer Zone',
  text: 'Answer the sum of the numbers',
  order: 7,
  component: BlitzFooter,
};
