import { GAME_CATEGORIES, GAME_TYPES } from 'core/constants/gameTypes';
import dark from 'core/constants/themes/dark';

export const EXPLORE_MODULES = {
  BLITZ_GAMES: 'BLITZ_GAMES',
  // CLASSICAL_GAMES: 'CLASSICAL_GAMES',
  PUZZLES: 'PUZZLES',
  MEMORY_GAMES: 'MEMORY_GAMES',
  // PUZZLE_GAMES: 'PUZZLE_GAMES',
};

export const EXPLORE_FEATURES = {
  // blitz
  ONLINE_DUELS: 'ONLINE_DUELS',
  FASTEST_FINGER: 'FASTEST_FINGER',

  // classical
  ABILITY_DUELS: 'ABILITY_DUELS',

  // memory
  FLASH_ANZAN: 'FLASH_ANZAN',

  // puzzle dules
  PUZZLE_DUELS: 'PUZZLE_DUELS',

  // puzzles
  CROSS_MATH_PUZZLE: 'CROSS_MATH_PUZZLE',
  <PERSON><PERSON>_KEN_PUZZLE: 'K<PERSON>_KEN_PUZZLE',
  MATH_MAZE: 'MATH_MAZE',

  // explore game
  EXPLORE_GAME: 'EXPLORE_GAME',
};

export const EXPLORE_MODULE_TITLE = {
  [EXPLORE_MODULES.BLITZ_GAMES]: 'Blitz',
  // [EXPLORE_MODULES.CLASSICAL_GAMES]: 'Classical',
  [EXPLORE_MODULES.MEMORY_GAMES]: 'Memory',
  [EXPLORE_MODULES.PUZZLES]: 'Daily Puzzle',
};

export const EXPLORE_MODULE_FEATURES = {
  [EXPLORE_MODULES.BLITZ_GAMES]: [
    EXPLORE_FEATURES.ONLINE_DUELS,
    EXPLORE_FEATURES.FASTEST_FINGER,
  ],
  // [EXPLORE_MODULES.CLASSICAL_GAMES]: [EXPLORE_FEATURES.ABILITY_DUELS],
  [EXPLORE_MODULES.MEMORY_GAMES]: [EXPLORE_FEATURES.FLASH_ANZAN],
  [EXPLORE_MODULES.PUZZLES]: [
    EXPLORE_FEATURES.CROSS_MATH_PUZZLE,
    EXPLORE_FEATURES.KEN_KEN_PUZZLE,
    // EXPLORE_FEATURES.MATH_MAZE,
  ],
};

export const MODULE_ACTIVATION_INFO = {
  [EXPLORE_MODULES.BLITZ_GAMES]: {
    title: 'BLITZ',
    description: 'fast-paced math duels',
    icon: '⚡',
    color: dark.colors.blitzGameColor,
    backgroundImage:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_MODULE.webp?timestamp=1751644817',
    tutorialImageIcon:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_TUTORIAL_ICON.webp?timestamp=1751650715',
    completionTime: '4',
  },
  // [EXPLORE_MODULES.CLASSICAL_GAMES]: {
  //   title: 'Classic Mode',
  //   description: 'fast-paced math duels',
  //   icon: '🎯',
  //   color: '#4ECDC4',
  //   backgroundImage:
  //     'https://cdn.matiks.com/files/66fc19d744b74099e82125b3_play-now-blitz.webp?timestamp=1750904324',
  //   tutorialImageIcon:
  //     'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_TUTORIAL_ICON.webp?timestamp=1751650715', // NEED TO UPDATE
  //   completionTime: '4',
  // },
  [EXPLORE_MODULES.MEMORY_GAMES]: {
    title: 'MEMORY',
    description: 'memory based calculations',
    icon: '🧠',
    color: dark.colors.memoryGameColor,
    backgroundImage:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_MEMORY_MODULE.webp?timestamp=1751644836',
    tutorialImageIcon:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_MEMORY_TUTORIAL_ICON.webp?timestamp=1751650716',
    completionTime: '4',
  },
  [EXPLORE_MODULES.PUZZLES]: {
    title: 'PUZZLE',
    description: 'grid based maths questions',
    icon: '🔍',
    color: dark.colors.puzzle.primary,
    backgroundImage:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_MODULE.webp?timestamp=1751644840',
    tutorialImageIcon:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_TUTORIAL_ICON.webp?timestamp=1751650717',
    completionTime: '4',
  },
};

export const FEATURE_ACTIVATION_INFO = {
  [EXPLORE_FEATURES.ONLINE_DUELS]: {
    title: 'Online Duels',
    description: 'OUTSCORE YOUR OPPONENT',
    icon: '⚔️',
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_DMAS_DUELS.webp?timestamp=1751644826',
  },
  [EXPLORE_FEATURES.FASTEST_FINGER]: {
    title: 'Fastest Fingers',
    description: 'THINK FAST - TYPE FAST',
    icon: '⚡',
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_DMAS_FFF_DUELS.webp?timestamp=1751644824',
  },
  [EXPLORE_FEATURES.ABILITY_DUELS]: {
    title: 'Ability Duels',
    description: 'Strategic mathematical battles testing various skills',
    icon: '🎯',
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_DMAS_DUELS.webp?timestamp=1751644826',
  },
  [EXPLORE_FEATURES.FLASH_ANZAN]: {
    title: 'Flash Anzan',
    description: 'Numbers flash. You calculate. That’s Anzan.',
    icon: '🧠',
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_FLASH_ANZAN.webp?timestamp=1751644829',
  },
  [EXPLORE_FEATURES.CROSS_MATH_PUZZLE]: {
    title: 'Cross Math Puzzle',
    description:
      'Complete the equations using BODMAS rule for each row and column.',
    icon: '🧩',
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_CROSS_MATH.webp?timestamp=1751644827',
  },
  [EXPLORE_FEATURES.KEN_KEN_PUZZLE]: {
    title: 'Ken Ken Puzzle',
    description: 'MATHEMATICAL SUDOKU',
    icon: '🔢',
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_KEN_KEN.webp?timestamp=1751644831',
  },
  [EXPLORE_FEATURES.MATH_MAZE]: {
    title: 'Math Maze',
    description:
      'CREATE EQUATIONS TO SOLVE THE TARGET SOLUTION (BODMAS NOT APPLICABLE) ',
    icon: '🌀',
  },
  [EXPLORE_FEATURES.EXPLORE_GAME]: {
    title: 'Explore Challenge',
    description: '15-second math sprint with 30 questions',
    icon: '🚀',
  },
};

export const ACTIVATION_GAME_CONFIG = {
  [EXPLORE_FEATURES.ONLINE_DUELS]: {
    gameMode: GAME_CATEGORIES.BLITZ,
    gameType: GAME_TYPES.PLAY_ONLINE,
    duration: 15,
    title: 'Play Your First Game',
    description: 'Solve questions as fast as you can',
    buttonLabel: 'PLAY 1-MIN BLITZ DUEL',
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_2.webp?timestamp=1751644821',
  },
  [EXPLORE_FEATURES.ABILITY_DUELS]: {
    gameMode: GAME_CATEGORIES.CLASSIC,
    gameType: GAME_TYPES.DMAS_ABILITY,
    duration: 15,
    title: 'Play Your First Game',
    description: 'Solve questions as fast as you can',
    buttonLabel: 'PLAY 1-MIN ABILITY DUEL',
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_2.webp?timestamp=1751644821',
  },
  [EXPLORE_FEATURES.FASTEST_FINGER]: {
    gameMode: GAME_CATEGORIES.BLITZ,
    gameType: GAME_TYPES.FASTEST_FINGER,
    duration: 15,
    title: 'Play Your First Game',
    description: 'Solve questions as fast as you can',
    buttonLabel: 'PLAY 1-MIN FASTEST FINGER DUEL',
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_2.webp?timestamp=1751644821',
  },
  [EXPLORE_FEATURES.FLASH_ANZAN]: {
    gameMode: GAME_CATEGORIES.MEMORY,
    gameType: GAME_TYPES.FLASH_ANZAN,
    duration: 15,
    title: 'Play Your First Game',
    description: 'Add them at speed and digits as fast as you can',
    buttonLabel: 'PLAY FLASH ANZAN DUEL',
    color: dark.colors.memoryGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_MEMORY_STEP_2.webp?timestamp=1751644839',
  },
  [EXPLORE_FEATURES.EXPLORE_GAME]: {
    gameMode: 'EXPLORE',
    gameType: 'EXPLORE_CHALLENGE',
    duration: 15,
    title: 'Try the Explore Challenge',
    description: 'Test your math skills in 15 seconds',
    buttonLabel: 'START CHALLENGE',
    color: '#FF6B6B',
    imageUrl: 'https://cdn.matiks.com/files/explore-challenge.webp',
  },

  [EXPLORE_FEATURES.CROSS_MATH_PUZZLE]: {
    title: 'Solve Your First Puzzle',
    description: 'Solve Puzzle as fast as you can',
    buttonLabel: 'SOLVE PUZZLE',
    color: dark.colors.puzzle.primary,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_STEP_2.webp?timestamp=1751644843',
  },
  [EXPLORE_FEATURES.KEN_KEN_PUZZLE]: {
    duration: 15,
    title: 'Play Your First Puzzle',
    description: 'Solve Puzzle as fast as you can',
    buttonLabel: 'SOLVE PUZZLE',
    color: dark.colors.puzzle.primary,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_STEP_2.webp?timestamp=1751644843',
  },
};

export const FEATURE_ACTIVATION_INFOS = {
  [EXPLORE_FEATURES.ONLINE_DUELS]: {
    title: 'Blitz Is Simple',
    description: 'Learn in 6 simple steps',
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_1.webp?timestamp=1751644820',
  },
  [EXPLORE_FEATURES.FASTEST_FINGER]: {
    title: 'Fastest Finger First Is Simple',
    description: 'Learn in 6 simple steps',
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_1.webp?timestamp=1751644820',
  },
  [EXPLORE_FEATURES.ABILITY_DUELS]: {
    title: 'Ability Duel Is Simple',
    description: 'Learn in 6 simple steps',
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_1.webp?timestamp=1751644820',
  },
  [EXPLORE_FEATURES.FLASH_ANZAN]: {
    title: 'Flash Anzan Is Simple',
    description: 'Learn in 10 simple steps',
    color: dark.colors.memoryGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_MEMORY_STEP_1.webp?timestamp=1751644836',
  },
  [EXPLORE_FEATURES.CROSS_MATH_PUZZLE]: {
    title: 'Puzzle Is Simple',
    description: 'Learn in 8 simple steps',
    color: dark.colors.puzzle.primary,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_STEP_1.webp?timestamp=1751644842',
  },
  [EXPLORE_FEATURES.KEN_KEN_PUZZLE]: {
    title: 'KenKen Puzzle',
    description: 'Learn in 10 simple steps',
    color: dark.colors.puzzle.primary,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_STEP_1.webp?timestamp=1751644842',
  },
};

export const WALKTHROUGH_STEPS_INFO_BY_FEATURE = {
  [EXPLORE_FEATURES.ONLINE_DUELS]: {
    totalSteps: 6,
    preDummyPlay: 3,
    postDummyPlay: 3,
  },
  [EXPLORE_FEATURES.FASTEST_FINGER]: {
    totalSteps: 6,
    preDummyPlay: 3,
    postDummyPlay: 3,
  },
  [EXPLORE_FEATURES.ABILITY_DUELS]: {
    totalSteps: 6,
    preDummyPlay: 3,
    postDummyPlay: 3,
  },
  [EXPLORE_FEATURES.FLASH_ANZAN]: {
    totalSteps: 10,
    preDummyPlay: 7,
    postDummyPlay: 3,
  },
  [EXPLORE_FEATURES.CROSS_MATH_PUZZLE]: {
    totalSteps: 10,
    preDummyPlay: 6,
    postDummyPlay: 4,
  },
  [EXPLORE_FEATURES.KEN_KEN_PUZZLE]: {
    totalSteps: 12,
    preDummyPlay: 8,
    postDummyPlay: 4,
  },
};

export const FINISHED_WALKTHROUGH_MODAL_INFO_BY_FEATURE = {
  [EXPLORE_FEATURES.ONLINE_DUELS]: {
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_3.webp?timestamp=1751644822',
  },
  [EXPLORE_FEATURES.FASTEST_FINGER]: {
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_3.webp?timestamp=1751644822',
  },
  [EXPLORE_FEATURES.ABILITY_DUELS]: {
    color: dark.colors.blitzGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_3.webp?timestamp=1751644822',
  },
  [EXPLORE_FEATURES.FLASH_ANZAN]: {
    color: dark.colors.memoryGameColor,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_BLITZ_STEP_3.webp?timestamp=1751644822', // NEED OF STEP_3 IMAGE
  },
  [EXPLORE_FEATURES.CROSS_MATH_PUZZLE]: {
    color: dark.colors.puzzle.primary,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_STEP_3.webp?timestamp=1751644844',
  },
  [EXPLORE_FEATURES.KEN_KEN_PUZZLE]: {
    color: dark.colors.puzzle.primary,
    imageUrl:
      'https://cdn.matiks.com/files/67c41a1e5c3f976082bb934a_PUZZLE_STEP_3.webp?timestamp=1751644844',
  },
};
