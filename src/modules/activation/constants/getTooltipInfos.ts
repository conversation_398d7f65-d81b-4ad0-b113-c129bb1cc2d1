import BlitzGameHeader from 'modules/activation/pages/ExploreBlitzGame/components/BlitzGameHeader';
import BlitzQuestion from 'modules/activation/pages/ExploreBlitzGame/components/BlitzQuestion';
import BlitzFooter from 'modules/activation/pages/ExploreBlitzGame/components/BlitzFooter';

export const CUSTOM_WALKTHROUGH_TOOLTIP_POSITION = {
  TOP: 'TOP',
  BOTTOM: 'BOTTOM',
};

export const BLITZ_GAME_WALKTHROUGH_STEPS = [
  {
    name: 'Scoreboard & Time',
    text: 'track ongoing duel stats',
    order: 1,
    component: BlitzGameHeader,
  },
  {
    name: 'About Playground',
    text: 'simple dmas problems at the top, solve and enter answer in the field below',
    order: 2,
    component: BlitzQuestion,
    walkthroughableViewStyle: {
      flex: 1,
      width: '100%',
    },
  },
  {
    name: 'Smart Answering',
    text: 'The answer submits automatically\n if correct',
    order: 3,
    component: BlitzFooter,
  },
];
