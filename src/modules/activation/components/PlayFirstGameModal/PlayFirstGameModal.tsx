import React from 'react';
import { Text, View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { Overlay } from '@rneui/themed';
import { Image } from 'expo-image';
import Pressable from 'atoms/Pressable';
import AntDesign from '@expo/vector-icons/AntDesign';
import _toUpper from 'lodash/toUpper';
import styles from './PlayFirstGameModal.style';

interface PlayFirstGameModalProps {
  isVisible: boolean;
  onClickPlayNow: () => void;
  gameConfig: {
    title: string;
    description: string;
    color: string;
    imageUrl: string;
    buttonLabel: string;
  };
}

const PlayFirstGameModal: React.FC<PlayFirstGameModalProps> = ({
  isVisible,
  onClickPlayNow,
  gameConfig,
}) => {
  const {
    title,
    description,
    color,
    imageUrl,
    buttonLabel = 'PROCEED',
  } = gameConfig ?? EMPTY_OBJECT;

  return (
    <Overlay
      isVisible={isVisible}
      overlayStyle={styles.overlayContainer}
      animationType="none"
      onBackdropPress={onClickPlayNow}
    >
      <View style={styles.contentContainer}>
        <Animatable.View
          animation="slideInUp"
          duration={600}
          style={[styles.bottomContent, { borderColor: color }]}
        >
          <Image
            style={styles.activationImage}
            source={imageUrl}
            contentFit="cover"
            transition={500}
          />
          <View
            style={{
              paddingHorizontal: 16,
              paddingTop: 32,
              gap: 24,
            }}
          >
            <View style={styles.infoTextsContainer}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.description}>{_toUpper(description)}</Text>
            </View>
            <View style={styles.buttonContainer}>
              <View style={styles.buttonWrapper}>
                <Pressable onPress={onClickPlayNow}>
                  <View style={styles.proceedButtonContainer}>
                    <Text style={[styles.startButtonText, { color }]}>
                      {buttonLabel}
                    </Text>
                    <AntDesign name="arrowright" size={16} color={color} />
                  </View>
                </Pressable>
              </View>
            </View>
          </View>
        </Animatable.View>
      </View>
    </Overlay>
  );
};

export default React.memo(PlayFirstGameModal);
