import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    margin: 0,
    padding: 0,
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingBottom: 40,
  },
  bottomContent: {
    backgroundColor: dark.colors.background,
    borderRadius: 24,
    overflow: 'hidden',
    gap: 16,
    marginHorizontal: 16,
    borderBottomWidth: 4,
    borderColor: '#FF932E',
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Montserrat-600',
    color: dark.colors.text,
    textAlign: 'center',
    letterSpacing: 0.1,
  },
  infoTextsContainer: {
    gap: 12,
  },
  description: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: withOpacity(dark.colors.text, 0.6),
    textAlign: 'center',
    lineHeight: 16,
    textTransform: 'uppercase',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 32,
    height: 79,
  },
  buttonWrapper: {
    flex: 1,
  },
  skipButtonText: {
    color: dark.colors.textDark,
    fontSize: 12,
    lineHeight: 16,
    fontFamily: 'Montserrat-800',
  },
  proceedButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    gap: 6,
  },
  startButtonText: {
    color: dark.colors.blitzGameColor,
    fontSize: 12,
    lineHeight: 16,
    fontFamily: 'Montserrat-800',
    textTransform: 'uppercase',
  },

  // image
  activationImage: {
    width: '100%',
    height: 200,
    overflow: 'hidden',
  },
});

export default styles;
