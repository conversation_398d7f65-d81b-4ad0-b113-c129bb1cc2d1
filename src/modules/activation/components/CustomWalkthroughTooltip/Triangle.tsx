import React from 'react';
import { View } from 'react-native';
import styles from 'modules/activation/pages/ExploreBlitzGame/ExploreBlitzGame.style';
import { CUSTOM_WALKTHROUGH_TOOLTIP_POSITION } from 'modules/activation/constants/getTooltipInfos';

const Triangle = ({
  direction,
  color,
}: {
  direction: string;
  color: string;
}) => (
  <View
    style={[
      styles.triangle,
      {
        borderTopColor:
          direction === CUSTOM_WALKTHROUGH_TOOLTIP_POSITION.BOTTOM
            ? color
            : 'transparent',
        borderBottomColor:
          direction === CUSTOM_WALKTHROUGH_TOOLTIP_POSITION.TOP
            ? color
            : 'transparent',
      },
      direction === CUSTOM_WALKTHROUGH_TOOLTIP_POSITION.BOTTOM &&
        styles.invertedTriangle,
    ]}
  />
);
export default React.memo(Triangle);
