import { StyleSheet } from 'react-native';
import { withOpacity } from 'core/utils/colorUtils';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    minHeight: 130,
  },
  carousel: {
    gap: 12,
  },
  carouselItemContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  exploreSectionContainer: {
    gap: 8,
  },
  exploreTitleText: {
    fontSize: 10,
    paddingHorizontal: 16,
    paddingVertical: 4,
    fontFamily: 'Montserrat-600',
    color: withOpacity(dark.colors.text, 0.4),
  },
});

export default styles;
