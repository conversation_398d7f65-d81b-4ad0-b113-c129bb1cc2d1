import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  card: {
    backgroundColor: dark.colors.primary,
    borderRadius: 16,
    height: 130,
    width: 240,
    overflow: 'hidden',
  },
  activeCard: {
    transform: [{ scale: 1.02 }],
    shadowOpacity: 0.4,
    elevation: 12,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  icon: {
    fontSize: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  imageBackground: {
    flex: 1,
    justifyContent: 'space-between',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  topContent: {
    paddingHorizontal: 12,
    paddingTop: 20,
  },
  bottomContent: {
    paddingHorizontal: 10,
    paddingBottom: 12,
  },
  blitzModeText: {
    fontSize: 20,
    color: '#FFFFFF',
    fontFamily: 'Montserrat-800',
    letterSpacing: 0.5,
    textTransform: 'uppercase',
    fontStyle: 'italic',
    lineHeight: 24,
    paddingRight: 4,
  },
  completionTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 6,
    gap: 4,
    alignSelf: 'flex-start',
  },
  completionTimeText: {
    color: dark.colors.primary,
    fontSize: 10,
    fontFamily: 'Montserrat-800',
  },
  clockIcon: {},
  title: {
    fontSize: 24,
    color: dark.colors.text,
    marginBottom: 6,
    fontFamily: 'Montserrat-600',
  },
  description: {
    fontSize: 12,
    color: withOpacity(dark.colors.text, 0.6),
    marginBottom: 16,
    lineHeight: 20,
  },
  featuresContainer: {
    marginTop: 'auto',
  },
  featuresLabel: {
    fontSize: 12,
    color: dark.colors.text,
    marginBottom: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: dark.colors.tertiary,
    borderRadius: 2,
    marginRight: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.text,
  },
  exploredBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  exploredBadgeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default styles;
