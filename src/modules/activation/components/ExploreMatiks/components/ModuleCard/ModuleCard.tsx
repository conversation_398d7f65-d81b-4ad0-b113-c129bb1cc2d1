import React, { useCallback } from 'react';
import { ImageBackground, Text, TouchableOpacity, View } from 'react-native';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import dark from '@/src/core/constants/themes/dark';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import { openBottomSheet } from '@/src/components/molecules/BottomSheet/BottomSheet';
import ModuleAlreadyExploredSheet from 'modules/activation/components/ExploreMatiks/components/ModuleAlreadyExploredSheet';
import { MODULE_ACTIVATION_INFO } from '../../../../constants/explore';
import styles from './ModuleCard.style';

interface ModuleCardProps {
  module: {
    id: string;
    key: string;
    features: string[];
    isExplored: boolean;
  };
  onPress: () => void;
}

const ModuleCard: React.FC<ModuleCardProps> = ({ module, onPress }) => {
  const displayInfo =
    MODULE_ACTIVATION_INFO[module.id as keyof typeof MODULE_ACTIVATION_INFO];

  const { title, color, backgroundImage, completionTime } = displayInfo;
  const { isExplored } = module;

  const handlePress = useCallback(() => {
    if (isExplored) {
      openBottomSheet({
        content: () => <ModuleAlreadyExploredSheet displayInfo={displayInfo} />,
        snapPoints: [50],
        styles: {
          frame: {
            borderTopColor: color,
            height: 389,
          },
        },
      });
    } else {
      onPress();
    }
  }, [displayInfo, isExplored, onPress, color]);

  if (!displayInfo) {
    return null;
  }

  return (
    <TouchableOpacity
      style={[styles.card]}
      onPress={handlePress}
      // activeOpacity={0.8}
    >
      <ImageBackground
        source={{ uri: backgroundImage }}
        style={styles.imageBackground}
      >
        <View style={styles.topContent}>
          <TextWithShadow
            text={title}
            textStyle={styles.blitzModeText}
            shadowOffsetY={-4}
            shadowOffsetX={0}
            shadowColor={dark.colors.primary}
          />
          <TextWithShadow
            text="MODE"
            textStyle={styles.blitzModeText}
            shadowOffsetY={-4}
            shadowOffsetX={2}
            shadowColor={dark.colors.primary}
          />
        </View>
        <View style={[styles.bottomContent]}>
          {completionTime && !isExplored && (
            <View
              style={[
                styles.completionTimeContainer,
                { backgroundColor: color },
              ]}
            >
              <Icon name="clock-o" color={dark.colors.primary} size={14} />
              <Text
                style={styles.completionTimeText}
              >{`${completionTime} MINS TO COMPLETE`}</Text>
            </View>
          )}
          {isExplored && (
            <View
              style={[
                styles.completionTimeContainer,
                { backgroundColor: color },
              ]}
            >
              <Icon
                name="checkcircle"
                type={ICON_TYPES.ANT_DESIGN}
                color={dark.colors.primary}
                size={12}
              />
              <Text style={styles.completionTimeText}>COMPLETED</Text>
            </View>
          )}
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default React.memo(ModuleCard);
