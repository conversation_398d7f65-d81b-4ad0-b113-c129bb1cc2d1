import React from 'react';
import { Alert, Text, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import ExploreMatiks from './ExploreMatiks';

// Example usage component
const ExploreMatiks = () => {
  const handleModulePress = (moduleId: string) => {
    Alert.alert('Module Selected', `You selected: ${moduleId}`);
    // Here you would typically navigate to the module or perform some action
  };

  const handleFeatureExplored = (featureType: string) => {
    Alert.alert('Feature Explored!', `You explored: ${featureType}`);
    // Here you could show a celebration animation or update UI
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: dark.colors.background,
        paddingTop: 50,
      }}
    >
      <Text
        style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: dark.colors.text,
          textAlign: 'center',
          marginBottom: 20,
        }}
      >
        Explore Matiks
      </Text>

      <ExploreMatiks
        onModulePress={handleModulePress}
        onFeatureExplored={handleFeatureExplored}
      />
    </View>
  );
};

export default ExploreMatiks;
