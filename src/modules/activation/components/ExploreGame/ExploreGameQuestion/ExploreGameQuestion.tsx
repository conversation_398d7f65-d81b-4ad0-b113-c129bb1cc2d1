import React from 'react';
import <PERSON><PERSON><PERSON><PERSON> from 'shared/QuestionsRenderer';
import { GAME_TYPES } from 'modules/game/constants/game';
import AbilityQuestion from 'modules/game/pages/PlayGame/AbilityQuestion';
import { DummyQuestion } from '../../../store/useExploreGameStore';

interface ExploreGameQuestionProps {
  game: any;
  question: DummyQuestion;
}

const ExploreGameQuestion: React.FC<ExploreGameQuestionProps> = ({
  game,
  question,
}) => {
  const { gameType } = game ?? EMPTY_OBJECT;

  if (
    gameType === GAME_TYPES.ABILITY_DUELS ||
    gameType === GAME_TYPES.DMAS_ABILITY
  ) {
    return <AbilityQuestion question={question} />;
  }

  return <QuestionsRenderer question={question} />;
};

export default React.memo(ExploreGameQuestion);
