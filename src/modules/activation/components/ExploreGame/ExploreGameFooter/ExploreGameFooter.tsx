import React, { useCallback } from 'react';
import Footer from 'modules/game/pages/PlayGame/Footer';
import gameReader from 'core/readers/gameReader';
import _isEmpty from 'lodash/isEmpty';

interface ExploreGameFooterProps {
  game: any;
  isGameActive: boolean;
  currentQuestion: any;
  submitAnswer: (value: number) => void;
}

const ExploreGameFooter: React.FC<ExploreGameFooterProps> = ({
  game,
  isGameActive,
  currentQuestion,
  submitAnswer: submitAnswerFromProps,
}) => {
  const gameType = gameReader.gameType(game);

  const submitAnswer = useCallback(
    ({ value }: any) => {
      submitAnswerFromProps(value);
    },
    [submitAnswerFromProps],
  );

  if (_isEmpty(currentQuestion)) {
    return null;
  }

  return (
    <Footer
      question={currentQuestion}
      gameType={gameType}
      submitAnswer={submitAnswer}
      isGameActive={isGameActive}
    />
  );
};

export default React.memo(ExploreGameFooter);
