import React from 'react';
import { Text, View } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { Overlay } from '@rneui/themed';
import dark from 'core/constants/themes/dark';
import { Image } from 'expo-image';
import Pressable from 'atoms/Pressable';
import AntDesign from '@expo/vector-icons/AntDesign';
import styles from './FinishedWalkthroughModal.style';

interface FinishedWalkthroughModalProps {
  isVisible: boolean;
  onFinished: () => void;
  finishedConfig: {
    color: string;
    imageUrl: string;
  };
  onBackdropPress: () => void;
}

const FinishedWalkthroughModal: React.FC<FinishedWalkthroughModalProps> = ({
  isVisible,
  onFinished,
  onBackdropPress,
  finishedConfig,
}) => {
  const { imageUrl, color = dark.colors.blitzGameColor } =
    finishedConfig ?? EMPTY_OBJECT;

  return (
    <Overlay
      isVisible={isVisible}
      overlayStyle={styles.overlayContainer}
      animationType="none"
      onBackdropPress={onBackdropPress}
    >
      <View style={styles.contentContainer}>
        <Animatable.View
          animation="slideInUp"
          duration={600}
          style={[styles.bottomContent, { borderColor: color }]}
        >
          <Image
            style={styles.activationImage}
            source={imageUrl}
            contentFit="contain"
            transition={500}
          />
          <View
            style={{
              paddingHorizontal: 16,
              paddingTop: 32,
              gap: 32,
            }}
          >
            <View style={styles.textContainer}>
              <Text style={styles.title}>Thats All Folks</Text>
              <Text style={styles.description}>
                that is all there is to know
              </Text>
            </View>
            <View style={styles.buttonContainer}>
              <View style={styles.buttonWrapper}>
                {/*  <Pressable onPress={onSkip}> */}
                {/*    <View style={styles.proceedButtonContainer}> */}
                {/*      <Text style={styles.skipButtonText}>GO BACK</Text> */}
                {/*    </View> */}
                {/*  </Pressable> */}
                {/* </View> */}
                <View style={styles.buttonWrapper}>
                  <Pressable onPress={onFinished}>
                    <View style={styles.proceedButtonContainer}>
                      <Text style={[styles.startButtonText, { color }]}>
                        PROCEED
                      </Text>
                      <AntDesign name="arrowright" size={16} color={color} />
                    </View>
                  </Pressable>
                </View>
              </View>
            </View>
          </View>
        </Animatable.View>
      </View>
    </Overlay>
  );
};

export default React.memo(FinishedWalkthroughModal);
