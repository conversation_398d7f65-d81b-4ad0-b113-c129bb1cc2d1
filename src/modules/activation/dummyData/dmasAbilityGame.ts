import { GAME_MODES, GAME_STATUS } from 'modules/game/constants/game';
import { GAME_CATEGORIES, GAME_TYPES } from 'core/constants/gameTypes';
import { QUESTION_CATEGORIES } from 'core/constants/questionCategories';
import {
  DUMMY_PLAYER_DATA,
  getDefaultLeaderboard,
  getPlayerFromUser,
} from 'modules/activation/dummyData/dummyBlitzGame';

export const DUMMY_QUESTIONS = [
  {
    id: 'sampleQuestion_46',
    expression: ['5', 'HCF', '5'],
    answers: ['5'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_69',
    expression: ['6', 'LCM', '5'],
    answers: ['30'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_78',
    expression: ['10'],
    answers: ['2', '5'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_36',
    expression: ['30', '='],
    answers: ['4² + 1² + 3² + 2²'],
    category: QUESTION_CATEGORIES.SUM_OF_SQUARES,
    presetIdentifier: 'SOS_1,1,1,1',
  },
  {
    id: 'sampleQuestion_20',
    expression: ['53', 'MOD', '6'],
    answers: ['5'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_39',
    expression: ['2', '✓', '81'],
    answers: ['9'],
    category: QUESTION_CATEGORIES.ROOT,
    presetIdentifier: 'ROOT_2,1',
  },
  {
    id: 'sampleQuestion_30',
    expression: ['35', 'MOD', '6'],
    answers: ['5'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_43',
    expression: ['2', '✓', '16'],
    answers: ['4'],
    category: QUESTION_CATEGORIES.ROOT,
    presetIdentifier: 'ROOT_2,1',
  },
  {
    id: 'sampleQuestion_93',
    expression: ['221'],
    answers: ['13', '17'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_3',
  },
  {
    id: 'sampleQuestion_10',
    expression: ['65', 'MOD', '6'],
    answers: ['5'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_24',
    expression: ['63', 'MOD', '4'],
    answers: ['3'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_55',
    expression: ['5', 'HCF', '4'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_38',
    expression: ['94', '='],
    answers: ['5² + 1² + 2² + 8²'],
    category: QUESTION_CATEGORIES.SUM_OF_SQUARES,
    presetIdentifier: 'SOS_1,1,1,1',
  },
  {
    id: 'sampleQuestion_90',
    expression: ['22'],
    answers: ['2', '11'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_31',
    expression: ['35', 'MOD', '5'],
    answers: ['0'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_34',
    expression: ['20', '='],
    answers: ['3² + 3² + 1² + 1²'],
    category: QUESTION_CATEGORIES.SUM_OF_SQUARES,
    presetIdentifier: 'SOS_1,1,1,1',
  },
  {
    id: 'sampleQuestion_91',
    expression: ['33'],
    answers: ['3', '11'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_37',
    expression: ['58', '='],
    answers: ['1² + 4² + 5² + 4²'],
    category: QUESTION_CATEGORIES.SUM_OF_SQUARES,
    presetIdentifier: 'SOS_1,1,1,1',
  },
  {
    id: 'sampleQuestion_53',
    expression: ['3', 'HCF', '6'],
    answers: ['3'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_63',
    expression: ['3', 'LCM', '4'],
    answers: ['12'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_48',
    expression: ['5', 'HCF', '6'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_72',
    expression: ['3', 'LCM', '6'],
    answers: ['6'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_25',
    expression: ['55', 'MOD', '6'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_60',
    expression: ['3', 'HCF', '4'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_64',
    expression: ['5', 'LCM', '5'],
    answers: ['5'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_33',
    expression: ['77', '='],
    answers: ['6² + 1² + 2² + 6²'],
    category: QUESTION_CATEGORIES.SUM_OF_SQUARES,
    presetIdentifier: 'SOS_1,1,1,1',
  },
  {
    id: 'sampleQuestion_50',
    expression: ['3', 'HCF', '5'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_62',
    expression: ['6', 'LCM', '6'],
    answers: ['6'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_23',
    expression: ['44', 'MOD', '5'],
    answers: ['4'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_42',
    expression: ['2', '✓', '25'],
    answers: ['5'],
    category: QUESTION_CATEGORIES.ROOT,
    presetIdentifier: 'ROOT_2,1',
  },
  {
    id: 'sampleQuestion_88',
    expression: ['91'],
    answers: ['7', '13'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_65',
    expression: ['4', 'LCM', '4'],
    answers: ['4'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_92',
    expression: ['57'],
    answers: ['3', '19'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_79',
    expression: ['169'],
    answers: ['13', '13'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_3',
  },
  {
    id: 'sampleQuestion_71',
    expression: ['6', 'LCM', '3'],
    answers: ['6'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_41',
    expression: ['2', '✓', '64'],
    answers: ['8'],
    category: QUESTION_CATEGORIES.ROOT,
    presetIdentifier: 'ROOT_2,1',
  },
  {
    id: 'sampleQuestion_83',
    expression: ['323'],
    answers: ['17', '19'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_3',
  },
  {
    id: 'sampleQuestion_82',
    expression: ['49'],
    answers: ['7', '7'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_40',
    expression: ['2', '✓', '49'],
    answers: ['7'],
    category: QUESTION_CATEGORIES.ROOT,
    presetIdentifier: 'ROOT_2,1',
  },
  {
    id: 'sampleQuestion_11',
    expression: ['46', 'MOD', '6'],
    answers: ['4'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_89',
    expression: ['9'],
    answers: ['3', '3'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_1',
  },
  {
    id: 'sampleQuestion_80',
    expression: ['85'],
    answers: ['5', '17'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_7',
    expression: ['36', 'MOD', '6'],
    answers: ['0'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_75',
    expression: ['4', 'LCM', '3'],
    answers: ['12'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_51',
    expression: ['6', 'HCF', '5'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_77',
    expression: ['34'],
    answers: ['2', '17'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_2',
  },
  {
    id: 'sampleQuestion_76',
    expression: ['5', 'LCM', '6'],
    answers: ['30'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_6',
    expression: ['34', 'MOD', '6'],
    answers: ['4'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_67',
    expression: ['3', 'LCM', '3'],
    answers: ['3'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_35',
    expression: ['21', '='],
    answers: ['3² + 2² + 2² + 2²'],
    category: QUESTION_CATEGORIES.SUM_OF_SQUARES,
    presetIdentifier: 'SOS_1,1,1,1',
  },
  {
    id: 'sampleQuestion_12',
    expression: ['56', 'MOD', '3'],
    answers: ['2'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_70',
    expression: ['4', 'LCM', '6'],
    answers: ['12'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_85',
    expression: ['143'],
    answers: ['11', '13'],
    category: QUESTION_CATEGORIES.PRIME_FACTORIZATION,
    presetIdentifier: 'PF_3',
  },
  {
    id: 'sampleQuestion_49',
    expression: ['4', 'HCF', '3'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_45',
    expression: ['5', 'HCF', '3'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_74',
    expression: ['3', 'LCM', '5'],
    answers: ['15'],
    category: QUESTION_CATEGORIES.LCM,
    presetIdentifier: 'LCM_1,1',
  },
  {
    id: 'sampleQuestion_8',
    expression: ['43', 'MOD', '6'],
    answers: ['1'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_32',
    expression: ['14', '='],
    answers: ['1² + 2² + 3²'],
    category: QUESTION_CATEGORIES.SUM_OF_SQUARES,
    presetIdentifier: 'SOS_1,1,1',
  },
  {
    id: 'sampleQuestion_22',
    expression: ['54', 'MOD', '4'],
    answers: ['2'],
    category: QUESTION_CATEGORIES.MOD,
    presetIdentifier: 'MOD_2,1',
  },
  {
    id: 'sampleQuestion_57',
    expression: ['6', 'HCF', '4'],
    answers: ['2'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
  {
    id: 'sampleQuestion_58',
    expression: ['4', 'HCF', '4'],
    answers: ['4'],
    category: QUESTION_CATEGORIES.HCF,
    presetIdentifier: 'HCF_1,1',
  },
];

export const DUMMY_DMAS_ABILITY_GAME = {
  _id: 'sampleQuestion',
  gameCategory: GAME_CATEGORIES.BLITZ,
  gameMode: GAME_MODES.ONLINE_SEARCH,
  gameType: GAME_TYPES.DMAS_ABILITY,
  gameStatus: GAME_STATUS.STARTED,
  questions: DUMMY_QUESTIONS,
  players: [],
  config: {
    timeLimit: 15,
    numPlayers: 2,
  },
  leaderBoard: [],
};

export const getDummyDmasAbilityGame = ({
  currentUser,
}: {
  currentUser: any;
}) => {
  const players = [
    getPlayerFromUser(currentUser),
    getPlayerFromUser(DUMMY_PLAYER_DATA),
  ];

  const leaderboard = [
    {
      ...getDefaultLeaderboard(currentUser),
      rank: 1,
      isWinner: true,
      ratingChange: 24,
      statikCoinsEarned: 10,
    },
    getDefaultLeaderboard(DUMMY_PLAYER_DATA),
  ];

  return {
    ...DUMMY_DMAS_ABILITY_GAME,
    players,
    leaderBoard: leaderboard,
  };
};
