import { encryptJsonData } from 'core/utils/encryptions/decrypt';

const KEN_KEN_DUMMY_PUZZLE_STRING =
  'KenKen Puzzle (Size: 5)\nGroups:\nGroup 1: 2 Cells: (0,0)\nGroup 2: 5 Cells: (0,1)\nGroup 3: 3 Cells: (0,2)\nGroup 4: 1 Cells: (0,3)\nGroup 5: 4 Cells: (0,4)\nGroup 6: +4 Cells: (1,0), (2,0)\nGroup 7: -3 Cells: (1,1), (2,1)\nGroup 8: -3 Cells: (1,2), (1,3)\nGroup 9: 3 Cells: (1,4)\nGroup 10: 4 Cells: (2,2)\nGroup 11: -2 Cells: (2,3), (3,3)\nGroup 12: 5 Cells: (2,4)\nGroup 13: ×20 Cells: (3,0), (4,0)\nGroup 14: 3 Cells: (3,1)\nGroup 15: 1 Cells: (3,2)\nGroup 16: -1 Cells: (3,4), (4,4)\nGroup 17: 2 Cells: (4,1)\nGroup 18: +8 Cells: (4,2), (4,3)\n\nBoard:\n0  1  2  3  4\n0 [2][1][3][5][4]\n1 [5][4][1][3][2]\n2 [3][2][4][1][5]\n3 [1][5][2][4][3]\n4 [4][3][5][2][1]\n\nSolution:\n2 1 3 5 4\n5 4 1 3 2\n3 2 4 1 5\n1 5 2 4 3\n4 3 5 2 1';

export const DUMMY_KENKEN_PUZZLE_DATA = {
  id: 'dummy-kenken-puzzle',
  typeSpecific: {
    kenKen: {
      puzzleString: encryptJsonData(KEN_KEN_DUMMY_PUZZLE_STRING),
    },
  },
  difficulty: 'easy',
  puzzleDate: '2025-01-01',
};

export const DUMMY_CROSS_MATH_PUZZLE_DATA = {
  id: 'dummy-cross-math-puzzle',
  difficulty: 'easy',
  cells: [
    [
      {
        id: '',
        value: '2',
        type: 'Operand',
        isVisible: true,
      },
      {
        id: '',
        value: '+',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '39',
        type: 'Operand',
        isVisible: true,
      },
      {
        id: '',
        value: '+',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '352',
        type: 'Operand',
        isVisible: false,
      },
      {
        id: '',
        value: '=',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '393',
        type: 'Operand',
        isVisible: true,
      },
    ],
    [
      {
        id: '',
        value: '×',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '÷',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '÷',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
    ],
    [
      {
        id: '',
        value: '52',
        type: 'Operand',
        isVisible: true,
      },
      {
        id: '',
        value: '÷',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '13',
        type: 'Operand',
        isVisible: false,
      },
      {
        id: '',
        value: '-',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '44',
        type: 'Operand',
        isVisible: true,
      },
      {
        id: '',
        value: '=',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '-40',
        type: 'Operand',
        isVisible: true,
      },
    ],
    [
      {
        id: '',
        value: '-',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '+',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '-',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
    ],
    [
      {
        id: '',
        value: '6',
        type: 'Operand',
        isVisible: true,
      },
      {
        id: '',
        value: '×',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '80',
        type: 'Operand',
        isVisible: false,
      },
      {
        id: '',
        value: '÷',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '10',
        type: 'Operand',
        isVisible: true,
      },
      {
        id: '',
        value: '=',
        type: 'Operator',
        isVisible: true,
      },
      {
        id: '',
        value: '48',
        type: 'Operand',
        isVisible: true,
      },
    ],
    [
      {
        id: '',
        value: '=',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '=',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '=',
        type: 'Operator',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
    ],
    [
      {
        id: '',
        value: '98',
        type: 'Operand',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '83',
        type: 'Operand',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        id: '',
        value: '-2',
        type: 'Operand',
        isVisible: true,
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
      {
        value: '',
        type: 'EmptyBlock',
        isVisible: true,
        id: '',
      },
    ],
  ],
  availableAnswers: ['352', '13', '80'],
};
