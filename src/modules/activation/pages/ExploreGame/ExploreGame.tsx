import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { Redirect, useLocalSearchParams, useRouter } from 'expo-router';
import { useSession } from 'modules/auth/containers/AuthProvider';
import _toInteger from 'lodash/toInteger';
import gameReader from 'core/readers/gameReader';
import Loading from 'atoms/Loading';
import { GAME_TYPES } from 'core/constants/gameTypes';
import PlayDummyFlashAnzanDuelGame from 'modules/activation/pages/PlayDummyFlashAnzanGame/PlayDummyFlashAnzanGame';
import _isNil from 'lodash/isNil';
import useExploreGameStore from '../../store/useExploreGameStore';
import ExploreGameHeader from '../../components/ExploreGame/ExploreGameHeader';
import ExploreGameQuestion from '../../components/ExploreGame/ExploreGameQuestion';
import ExploreGameFooter from '../../components/ExploreGame/ExploreGameFooter';
import styles from './ExploreGame.style';

const ExploreDMASAndDMASAbilityGame = ({
  gameType,
  feature,
}: {
  gameType: string;
  feature: string;
}) => {
  const router = useRouter();
  const { user } = useSession();

  const [questionStartTime, setQuestionStartTime] = useState<number>(
    Date.now(),
  );

  const {
    currentGame,
    isGameActive,
    isGameCompleted,
    gameTimer,
    initializeGame,
    endGame,
    submitAnswer,
    handleAnswerSubmissionByBot,
    updateTimer,
  } = useExploreGameStore();

  const questions = gameReader.questions(currentGame);

  const userRef = useRef(user);
  userRef.current = user;

  useEffect(() => {
    initializeGame({
      currentUser: userRef.current,
      gameType,
    });
    setQuestionStartTime(Date.now());
  }, [gameType, initializeGame]);

  const handleGameEnd = useCallback(() => {
    if (!currentGame || !user) return;

    endGame();

    router.push(`/explore/game-result?gameType=${gameType}&feature=${feature}`);
  }, [currentGame, user, endGame, router, gameType, feature]);

  const gameTimerRef = useRef<NodeJS.Timeout | null>(null);
  const gameStartTime = currentGame?.gameStartTime;
  const gameTimeLimit = gameReader.timeLimit(currentGame);

  useEffect(() => {
    if (!isGameActive || isGameCompleted || !gameStartTime) return;

    clearInterval(gameTimerRef.current as any);
    gameTimerRef.current = setInterval(() => {
      const currentTime = getCurrentTime();
      if (gameStartTime <= currentTime) {
        const timeLeft = gameStartTime + gameTimeLimit * 1000 - currentTime;
        if (timeLeft > 0) {
          updateTimer(_toInteger(timeLeft / 1000));
        } else {
          handleGameEnd();
        }
      } else {
        //
      }
    }, 1000);

    return () => clearInterval(gameTimerRef.current as any);
  }, [
    isGameActive,
    handleGameEnd,
    isGameCompleted,
    gameTimeLimit,
    gameTimer,
    updateTimer,
    gameStartTime,
  ]);

  const handleAnswerSubmit = useCallback(
    (answer: number) => {
      if (!currentGame || !isGameActive) return;

      const currentQuestion = (questions as any)[
        currentGame.currentQuestionIndex
      ];
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);

      submitAnswer({
        questionId: currentQuestion.id,
        answer,
        timeSpent,
      });

      setTimeout(() => {
        const randProbability = Math.random();
        if (randProbability >= 0.2) {
          handleAnswerSubmissionByBot();
        }
      }, 1000);

      // Move to next question or end game
      if (currentGame.currentQuestionIndex < (questions as any[]).length - 1) {
        setQuestionStartTime(Date.now());
      } else {
        handleGameEnd();
      }
    },
    [
      currentGame,
      isGameActive,
      questions,
      questionStartTime,
      submitAnswer,
      handleAnswerSubmissionByBot,
      handleGameEnd,
    ],
  );

  if (!currentGame) {
    return <Loading label="Loading Game..." />;
  }

  if (isGameCompleted) {
    return (
      <Redirect
        href={`/explore/game-result?gameType=${gameType}&feature=${feature}`}
      />
    );
  }

  const currentQuestion = questions[currentGame.currentQuestionIndex];

  return (
    <View style={styles.container}>
      <ExploreGameHeader game={currentGame} />

      <View style={{ flex: 1 }}>
        <ExploreGameQuestion
          question={currentQuestion}
          onAnswerSubmit={handleAnswerSubmit}
          isGameActive={isGameActive}
          game={currentGame}
        />
      </View>

      <ExploreGameFooter
        game={currentGame}
        currentQuestion={currentQuestion}
        submitAnswer={handleAnswerSubmit}
        isGameActive={isGameActive}
        isGameCompleted={isGameCompleted}
      />
    </View>
  );
};

const ExploreGameContainer = () => {
  const searchParams = useLocalSearchParams();

  const { gameType, feature }: { gameType: string; feature: string } =
    searchParams ?? EMPTY_OBJECT;

  if (_isNil(gameType)) {
    return <Redirect href="/explore" />;
  }

  if (gameType === GAME_TYPES.FLASH_ANZAN) {
    return <PlayDummyFlashAnzanDuelGame />;
  }

  return (
    <ExploreDMASAndDMASAbilityGame gameType={gameType} feature={feature} />
  );
};

export default React.memo(ExploreGameContainer);
