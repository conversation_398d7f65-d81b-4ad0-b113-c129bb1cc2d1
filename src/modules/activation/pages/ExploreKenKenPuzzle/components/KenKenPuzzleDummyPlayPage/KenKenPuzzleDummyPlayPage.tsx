import React, { useCallback } from 'react';
import { SafeAreaView, View } from 'react-native';
import KenKenPuzzleQuestion from 'shared/KenKenPuzzleQuestion/KenKenPuzzleQuestion';
import { router } from 'expo-router';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import Header from 'shared/Header';
import usePuzzleWalkthroughStore from 'modules/activation/store/usePuzzleWalkthroughStore';
import { EXPLORE_FEATURES } from 'modules/activation/constants/explore';
import { DUMMY_KENKEN_PUZZLE_DATA } from 'modules/activation/dummyData/dummyPuzzleData';
import styles from './KenKenPuzzleDummyPlayPage.style';

const KenKenPuzzleDummyPlayPage = () => {
  const setResult = usePuzzleWalkthroughStore((state) => state.setResult);

  const onSubmitPuzzle = useCallback(
    ({ timeSpent }: { timeSpent: number }) => {
      setResult({ timeSpent });
      router.push(
        `/explore/puzzle-play-result?puzzleType=${PUZZLE_TYPES.KEN_KEN_PUZZLE}&feature=${EXPLORE_FEATURES.KEN_KEN_PUZZLE}`,
      );
    },
    [setResult],
  );

  return (
    <SafeAreaView style={styles.container}>
      <KenKenPuzzleQuestion
        puzzle={DUMMY_KENKEN_PUZZLE_DATA}
        onSubmitPuzzle={onSubmitPuzzle}
      >
        <Header
          renderTrailingComponent={() => <KenKenPuzzleQuestion.Timer />}
        />
        <View
          style={{
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <KenKenPuzzleQuestion.Grid />
        </View>

        <View style={{ height: 20 }} />
        <KenKenPuzzleQuestion.Actions />
        <View style={{ height: 20 }} />
        <KenKenPuzzleQuestion.Options />
      </KenKenPuzzleQuestion>
    </SafeAreaView>
  );
};

export default React.memo(KenKenPuzzleDummyPlayPage);
