import React from 'react';
import <PERSON><PERSON><PERSON><PERSON> from 'shared/QuestionsRenderer';
import {
  dummyDMASAbilityQuestion,
  dummyDMASQuestion,
} from 'modules/activation/dummyData/data';
import { useLocalSearchParams } from 'expo-router';
import { EXPLORE_FEATURES } from 'modules/activation/constants/explore';

import * as Animatable from 'react-native-animatable';

const BlitzQuestion = () => {
  const searchParams = useLocalSearchParams();

  const { feature } = searchParams ?? EMPTY_OBJECT;

  const question =
    feature === EXPLORE_FEATURES.ABILITY_DUELS
      ? dummyDMASAbilityQuestion
      : dummyDMASQuestion;

  return (
    <Animatable.View animation="fadeInDown" delay={200} style={{ flex: 1 }}>
      <QuestionRenderer question={question} />
    </Animatable.View>
  );
};

export default React.memo(BlitzQuestion);
