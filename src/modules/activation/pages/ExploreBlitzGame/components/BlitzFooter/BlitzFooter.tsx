import React from 'react';
import Footer from 'modules/game/pages/PlayGame/Footer';
import { dummyDMASQuestion } from 'modules/activation/dummyData/data';
import * as Animatable from 'react-native-animatable';

const propsToPass = {
  question: dummyDMASQuestion,
  submitAnswer: () => null,
};

const BlitzFooter = () => (
  <Animatable.View animation="fadeInUp" delay={200}>
    <Footer {...propsToPass} />
  </Animatable.View>
);

export default React.memo(BlitzFooter);
