import React, { useCallback, useEffect, useState } from 'react';
import {
  Dimensions,
  ImageBackground,
  SafeAreaView,
  StyleSheet,
  View,
} from 'react-native';

import * as Animatable from 'react-native-animatable';
import ActivationProgress from 'modules/activation/components/ActivationProgress';
import useGoBack from 'navigator/hooks/useGoBack';
import {
  Copilot<PERSON>rovider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import StartActivationModal from 'modules/activation/components/StartActivationModal';
import dark from 'core/constants/themes/dark';
import {
  ACTIVATION_GAME_CONFIG,
  EXPLORE_FEATURES,
  FEATURE_ACTIVATION_INFOS,
  WALKTHROUGH_STEPS_INFO_BY_FEATURE,
} from 'modules/activation/constants/explore';
import { router } from 'expo-router';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import CrossMathPuzzleQuestion from '@/src/components/shared/CrossMathPuzzleQuestion/CrossMathPuzzleQuestion';
import {
  CROSS_MATH_PUZZLE_IMAGE,
  CROSS_MATH_PUZZLE_OVERLAY_IMAGE,
} from 'modules/activation/constants/walkthroughImages';
import useExplorationStatusFromStore from 'store/useExploredFeaturesStore/useExplorationStatusFromStore';
import { DUMMY_CROSS_MATH_PUZZLE_DATA } from 'modules/activation/dummyData/dummyPuzzleData';
import CustomTooltip, {
  withCustomProps,
} from '../../components/CustomWalkthroughTooltip';
import PlayFirstGameModal from '../../components/PlayFirstGameModal';
import styles from './ExploreCrossMathPuzzle.style';
import CrossMathPuzzleQuestionActions from '../../../../components/shared/CrossMathPuzzleQuestion/components/Actions';
import CrossMathPuzzleQuestionOptions from '../../../../components/shared/CrossMathPuzzleQuestion/components/CrossMathPuzzleOptions';

const CrossMathPuzzleTooltip = withCustomProps(CustomTooltip, {
  color: dark.colors.puzzle.primary,
});

const WalkthroughableView = walkthroughable(View);

const ExploreCrossMathPuzzle = React.memo(({ feature }: any) => {
  const { goBack } = useGoBack();
  const { markFeatureAsExplored } = useExplorationStatusFromStore();

  const CROSS_MATH_PUZZLE_ACTIVATION_INFO = FEATURE_ACTIVATION_INFOS[feature];

  const STEPS_INFO = WALKTHROUGH_STEPS_INFO_BY_FEATURE[feature];

  const TOTAL_STEPS = STEPS_INFO.totalSteps;

  const [currentStep, setCurrentStep] = useState(0);
  const [
    playFirstCrossMathPuzzleModalVisible,
    setPlayFirstCrossMathPuzzleModalVisible,
  ] = useState(false);
  const [startActivationModalVisible, setStartActivationModalVisible] =
    useState(false);

  const { start, copilotEvents } = useCopilot();

  const onClickStep = useCallback((index: number) => {
    // if (index >= 1 && index <= TOTAL_STEPS) {
    //   setCurrentStep(index);
    // }
  }, []);

  const onPressSkip = useCallback(async () => {
    setStartActivationModalVisible(false);
    goBack();
    markFeatureAsExplored(feature);
  }, [feature, goBack, markFeatureAsExplored]);

  const onPressStartActivation = useCallback(() => {
    setStartActivationModalVisible(false);
    start();
  }, [start]);

  const onPressSolveCrossMathPuzzleGame = useCallback(async () => {
    setPlayFirstCrossMathPuzzleModalVisible(false);
    markFeatureAsExplored(feature);
    router.push(
      `/explore/puzzle-play?puzzleType=${PUZZLE_TYPES.CROSS_MATH_PUZZLE}&feature=${EXPLORE_FEATURES.CROSS_MATH_PUZZLE}`,
    );
  }, [feature, markFeatureAsExplored]);

  useEffect(() => {
    copilotEvents.on('stepChange', (step) => {
      setCurrentStep(step ? step.order : 0);
    });

    copilotEvents.on('stop', () => {
      setPlayFirstCrossMathPuzzleModalVisible(true);
    });
  }, [copilotEvents]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setStartActivationModalVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <CrossMathPuzzleQuestion
        puzzle={DUMMY_CROSS_MATH_PUZZLE_DATA as any}
        onSubmitPuzzle={NULL_FUN}
      >
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <View style={{ height: 60 }} />

          <Animatable.View
            animation="fadeInDown"
            delay={200}
            style={{
              width: 300,
              height: 300,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <ImageBackground
              source={{
                uri: CROSS_MATH_PUZZLE_IMAGE,
              }}
              style={{
                width: 300,
                height: 300,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <CopilotStep
                key="grid"
                text="This is your play arena"
                order={1}
                name="Playground"
              >
                <WalkthroughableView
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                />
              </CopilotStep>
              <CopilotStep
                key="operands"
                text="The numbers in the grid are called 'operands'. These are the values you'll use in the calculations."
                order={2}
                name="Operands"
              >
                <WalkthroughableView
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: 40,
                    height: 40,
                  }}
                />
              </CopilotStep>
              <CopilotStep
                key="operators"
                text="The symbols (+, -, ×, ÷) are 'operators'. They tell you what mathematical operation to perform."
                order={3}
                name="Operators"
              >
                <WalkthroughableView
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 42,
                    width: 40,
                    height: 40,
                  }}
                />
              </CopilotStep>
              <ImageBackground
                source={{
                  uri: CROSS_MATH_PUZZLE_OVERLAY_IMAGE,
                }}
                style={{
                  width: 300,
                  height: 300,
                  position: 'absolute',
                  opacity: currentStep === 4 ? 1 : 0,
                }}
                resizeMode="contain"
              >
                <CopilotStep
                  key="bodmas"
                  text="Complete the puzzle using BODMAS rules for every row and column."
                  order={4}
                  name="How to solve"
                >
                  <WalkthroughableView
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  />
                </CopilotStep>
              </ImageBackground>
            </ImageBackground>
          </Animatable.View>

          <View style={{ height: 20 }} />
          <CopilotStep
            key="actions"
            text="use undo, redo, or clear as needed."
            order={5}
            name="Tool Kits"
          >
            <WalkthroughableView>
              <Animatable.View animation="fadeInUp" delay={200}>
                <CrossMathPuzzleQuestionActions />
              </Animatable.View>
            </WalkthroughableView>
          </CopilotStep>
          <View style={{ height: 20 }} />
          <CopilotStep
            key="Play With Numbers"
            text="Pick a Number, tap a cell and complete the equations using BODMAS"
            order={6}
            name="Options"
          >
            <WalkthroughableView>
              <Animatable.View animation="fadeInUp" delay={200}>
                <CrossMathPuzzleQuestionOptions />
              </Animatable.View>
            </WalkthroughableView>
          </CopilotStep>
        </View>
      </CrossMathPuzzleQuestion>
      <StartActivationModal
        onSkip={onPressSkip}
        activationConfig={CROSS_MATH_PUZZLE_ACTIVATION_INFO}
        onStart={onPressStartActivation}
        isVisible={startActivationModalVisible}
      />
      <Animatable.View
        animation="slideInDown"
        duration={500}
        delay={500}
        style={[StyleSheet.absoluteFill, { zIndex: 1 }]}
      >
        <ActivationProgress
          currentStep={currentStep}
          stepsCount={TOTAL_STEPS}
          onClose={goBack}
          onClickStep={onClickStep}
        />
      </Animatable.View>
      <PlayFirstGameModal
        isVisible={playFirstCrossMathPuzzleModalVisible}
        onClickPlayNow={onPressSolveCrossMathPuzzleGame}
        gameConfig={ACTIVATION_GAME_CONFIG[feature] as any}
      />
    </SafeAreaView>
  );
});

const MARGIN = 16;
const WIDTH = Dimensions.get('window').width - 2 * MARGIN;

const ExploreCrossMathPuzzleContainer = (props: any) => (
  <CopilotProvider
    tooltipComponent={CrossMathPuzzleTooltip as any}
    tooltipStyle={{
      backgroundColor: 'transparent',
      padding: 0,
      borderRadius: 0,
      width: WIDTH,
      maxWidth: WIDTH,
      left: MARGIN,
    }}
    arrowColor="transparent"
    stopOnOutsideClick
    androidStatusBarVisible
    stepNumberComponent={() => null}
    backdropColor="rgba(0, 0, 0, 0.6)"
    labels={{
      previous: 'Previous',
      next: 'Next',
      skip: 'Skip',
      finish: 'Got it!',
    }}
  >
    <ExploreCrossMathPuzzle {...props} />
  </CopilotProvider>
);

export default React.memo(ExploreCrossMathPuzzleContainer);
