import { useCallback, useEffect, useRef, useState } from 'react';
import _reduce from 'lodash/reduce';
import _toNumber from 'lodash/toNumber';
import _size from 'lodash/size';
import _toString from 'lodash/toString';
import { FLASH_ANZAN_DEFAULT_CONFIG } from '@/src/modules/practice/constants/presetConfig';
import FLASH_ANZAN_GAME_PHASES from '@/src/modules/game/constants/flashAnzanGamePhases';
import { SOLUTION_STATUS } from '@/src/modules/game/pages/PlayGame/Footer/AnswerEvaluators';
import { generateNumbers } from 'modules/game/hooks/useHandleFlashAnzanPlay';
import useExploreGameStore from 'modules/activation/store/useExploreGameStore';
import getMaxScoreOfFlashAnzanQueFromConfig from 'modules/game/utils/getMaxScoreOfFlashAnzanQueByConfig';

const useHandleDummyFlashAnzanPlay = () => {
  const { submitAnswer: submitAnswerFromStore, handleAnswerSubmissionByBot } =
    useExploreGameStore();

  const initialQuestion = 1;

  const [updatedConfig, setConfig] = useState({
    ...FLASH_ANZAN_DEFAULT_CONFIG,
    noOfQuestions: 1,
    numberCount: 10,
    configSelectionTime: 7,
    startingCountdownTime: 3,
    answerTime: 5,
  });

  const updateConfig = useCallback(
    async (key, value) => {
      const newConfig = { ...updatedConfig, [key]: value };
      const newConfigWithNumbersCount = {
        ...newConfig,
        numberCount: Math.floor((15 * 1000) / newConfig.flashSpeed),
      };
      setConfig(newConfigWithNumbersCount);
    },
    [updatedConfig],
  );

  const {
    numberCount,
    noOfQuestions,
    flashSpeed,
    configSelectionTime,
    startingCountdownTime,
    answerTime,
  } = updatedConfig;

  const [gamePhase, setGamePhase] = useState(FLASH_ANZAN_GAME_PHASES.CONFIG);
  const [phaseCountdown, setPhaseCountdown] = useState(configSelectionTime);
  const [isGameCompleted, setIsGameCompleted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(initialQuestion);
  const [numbers, setNumbers] = useState([]);
  const [currentNumberIndex, setCurrentNumberIndex] = useState(-1);
  const [userAnswer, setUserAnswer] = useState('');
  const [isFlashing, setIsFlashing] = useState(false);
  const [isInputWrong, setIsInputWrong] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [solutionStatus, setSolutionStatus] = useState(
    SOLUTION_STATUS.NOT_ANSWERED,
  );

  const isSubmittingRef = useRef(false);
  const hasAttemptedSubmissionForCurrentQuestionRef = useRef(false);

  const startNextQuestion = useCallback(() => {
    const newNumbers = generateNumbers({ config: updatedConfig });
    hasAttemptedSubmissionForCurrentQuestionRef.current = false;
    isSubmittingRef.current = false;
    setNumbers(newNumbers);
    setCurrentNumberIndex(0);
    setIsFlashing(true);
    setUserAnswer('');
    setIsInputWrong(false);
    setIsCorrect(false);
    setSolutionStatus(SOLUTION_STATUS.NOT_ANSWERED);
  }, [updatedConfig]);

  const submitAnswer = useCallback(
    ({ answer }: { answer: any }) => {
      if (
        isSubmittingRef.current ||
        hasAttemptedSubmissionForCurrentQuestionRef.current
      ) {
        return;
      }

      isSubmittingRef.current = true;
      hasAttemptedSubmissionForCurrentQuestionRef.current = true;

      setPhaseCountdown(0);

      const correctAnswer = _reduce(numbers, (sum, num) => sum + num, 0);
      const actualSubmittedValue = answer ?? userAnswer;
      const isAnswerCorrect = _toNumber(actualSubmittedValue) === correctAnswer;

      setIsCorrect(isAnswerCorrect);
      setIsInputWrong(!isAnswerCorrect);
      setSolutionStatus(
        isAnswerCorrect ? SOLUTION_STATUS.CORRECT : SOLUTION_STATUS.INCORRECT,
      );

      try {
        if (isAnswerCorrect) {
          submitAnswerFromStore({
            answer: 0,
            questionId: '1',
            timeSpent: 30 * 1000,
            maxScore: getMaxScoreOfFlashAnzanQueFromConfig({
              config: updatedConfig,
            }),
          });
          const randProbability = Math.random();
          if (randProbability >= 0.2) {
            handleAnswerSubmissionByBot();
          }
        } else {
          submitAnswerFromStore({
            answer: 0,
            questionId: '1',
            timeSpent: 30 * 1000,
            maxScore: -10,
          });
        }

        if (currentQuestion < noOfQuestions) {
          setCurrentQuestion((prev) => prev + 1);
          setGamePhase(FLASH_ANZAN_GAME_PHASES.CONFIG);
          setPhaseCountdown(configSelectionTime);
        } else {
          setTimeout(() => {
            setGamePhase(FLASH_ANZAN_GAME_PHASES.COMPLETED);
            setIsGameCompleted(true);
          }, 1000);
        }
      } catch (e) {
        // console.error('Error submitting flash anzan answer:', e);
      } finally {
        isSubmittingRef.current = false;
      }
    },
    [
      numbers,
      userAnswer,
      submitAnswerFromStore,
      updatedConfig,
      currentQuestion,
      noOfQuestions,
      configSelectionTime,
    ],
  );

  useEffect(() => {
    if (isFlashing && currentNumberIndex < numberCount) {
      const numberTimer = setTimeout(() => {
        setCurrentNumberIndex((prev) => prev + 1);
      }, flashSpeed);
      return () => clearTimeout(numberTimer);
    }
    if (isFlashing && currentNumberIndex >= numberCount) {
      setIsFlashing(false);
      setGamePhase(FLASH_ANZAN_GAME_PHASES.ANSWERING);
      setPhaseCountdown(answerTime);
    }
  }, [
    currentNumberIndex,
    isFlashing,
    numberCount,
    flashSpeed,
    answerTime,
    setGamePhase,
    setPhaseCountdown,
    setIsFlashing,
  ]);

  const handleInputChange = useCallback(
    (text) => {
      setUserAnswer(text);
      const correctAnswer = _reduce(numbers, (acc, num) => acc + num, 0);
      const isCurrentInputCorrect = _toNumber(text) === correctAnswer;

      if (isCurrentInputCorrect) {
        submitAnswer({ answer: text });
      } else {
        setIsCorrect(false);
        if (_size(_toString(correctAnswer)) === _size(_toString(text))) {
          setIsInputWrong(true);
          setSolutionStatus(SOLUTION_STATUS.INCORRECT);
        }
      }
    },
    [numbers, submitAnswer, handleAnswerSubmissionByBot],
  );

  const handleForceSubmitAnswer = useCallback(() => {
    submitAnswer({ answer: userAnswer });
  }, [submitAnswer, userAnswer]);

  const startNextQuestionRef = useRef(startNextQuestion);
  startNextQuestionRef.current = startNextQuestion;
  const handleForceSubmitAnswerRef = useRef(handleForceSubmitAnswer);
  handleForceSubmitAnswerRef.current = handleForceSubmitAnswer;

  useEffect(() => {
    let countdownInterval: any;
    if (phaseCountdown > 0) {
      countdownInterval = setInterval(() => {
        setPhaseCountdown((prevCountdown) => Math.max(0, prevCountdown - 1));
      }, 1000);
    } else {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
      switch (gamePhase) {
        case FLASH_ANZAN_GAME_PHASES.CONFIG:
          setGamePhase(FLASH_ANZAN_GAME_PHASES.STARTING);
          setPhaseCountdown(startingCountdownTime);
          break;
        case FLASH_ANZAN_GAME_PHASES.STARTING:
          setGamePhase(FLASH_ANZAN_GAME_PHASES.FLASHING);
          startNextQuestionRef.current();
          break;
        case FLASH_ANZAN_GAME_PHASES.ANSWERING:
          if (
            !isSubmittingRef.current &&
            !hasAttemptedSubmissionForCurrentQuestionRef.current
          ) {
            handleForceSubmitAnswerRef.current();
          }
          break;
        default:
          break;
      }
    }
    return () => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
    };
  }, [
    gamePhase,
    phaseCountdown,
    startingCountdownTime,
    setPhaseCountdown,
    setGamePhase,
  ]);

  return {
    config: updatedConfig,
    gamePhase,
    phaseCountdown,
    isGameCompleted,
    isFlashing,
    isInputWrong,
    isCorrect,
    userAnswer,
    currentQuestion,
    currentNumberIndex,
    numbers,
    updateConfig,
    setUserAnswer: handleInputChange,
    solutionStatus,
  };
};

export default useHandleDummyFlashAnzanPlay;
