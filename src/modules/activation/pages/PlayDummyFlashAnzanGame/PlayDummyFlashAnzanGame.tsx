import React, { useEffect, useRef } from 'react';
import { Text, View } from 'react-native';
import CustomTextInput from 'shared/CustomTextInput';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';
import FLASH_ANZAN_GAME_PHASES from '@/src/modules/game/constants/flashAnzanGamePhases';
import useFlashAnzanPlayPageStyles from 'modules/game/pages/FlashAnzan/Components/PlayFlashAnzanDuelGame/PlayFlashAnzanDuelGame.style';
import FlashNumber from 'modules/game/pages/FlashAnzan/Components/PlayFlashAnzanDuelGame/FlashNumber';
import DummyFlashAnzanConfigSelection from 'modules/activation/pages/PlayDummyFlashAnzanGame/components/DummyFlashAnzanConfigSelection';
import useExploreGameStore from 'modules/activation/store/useExploreGameStore';
import useHandleDummyFlashAnzanPlay from 'modules/activation/pages/PlayDummyFlashAnzanGame/hooks/useHandleDummyFlashAnzanPlay';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { GAME_TYPES } from 'core/constants/gameTypes';
import { Redirect } from 'expo-router';
import _get from 'lodash/get';
import { EXPLORE_FEATURES } from '../../constants/explore';

const PlayDummyFlashAnzanDuelGame = () => {
  const styles = useFlashAnzanPlayPageStyles();
  const { user } = useSession();

  const { currentGame: game, initializeGame } = useExploreGameStore();

  const userRef = useRef(user);
  userRef.current = user;
  useEffect(() => {
    initializeGame({
      currentUser: userRef.current,
      gameType: GAME_TYPES.FLASH_ANZAN,
    });
  }, [initializeGame]);

  const { config: gameConfig, gameType } = game ?? EMPTY_OBJECT;

  const { isMobile: isCompactMode, isMobileBrowser } = useMediaQuery();

  const gameTypeRef = useRef(gameType);
  gameTypeRef.current = gameType;
  const configRef = useRef(gameConfig);
  configRef.current = gameConfig;

  const {
    config,
    gamePhase,
    isCorrect,
    isFlashing,
    isInputWrong,
    userAnswer,
    currentQuestion,
    currentNumberIndex,
    numbers,
    phaseCountdown,
    setUserAnswer,
    updateConfig,
    solutionStatus,
  } = useHandleDummyFlashAnzanPlay();

  const { noOfQuestions } = config ?? EMPTY_OBJECT;

  const inputProps = {
    value: userAnswer,
    style: [
      styles.input,
      isInputWrong && { borderColor: dark.colors.errorDark },
      isCorrect && { borderColor: dark.colors.secondary },
    ],
    keyboardAppearance: 'dark',
    placeholderTextColor: dark.colors.inputPlaceholder,
    solutionStatus,
    customKeyboard: isCompactMode,
    onChangeText: setUserAnswer,
    keyboardType: 'number-pad',
    contextMenuHidden: true,
    autoCorrect: false,
    blurOnSubmit: false,
    autoFocus: true,
    editable: isMobileBrowser ? false : !isCorrect,
  };

  const renderStartingCountdown = () => (
    <View style={styles.countdownContainer}>
      <Text style={styles.countdownText}>Starting in {phaseCountdown}</Text>
    </View>
  );

  const renderConfigSelection = () => (
    <DummyFlashAnzanConfigSelection
      selectedConfig={config}
      updateConfig={updateConfig}
      currentQuestion={currentQuestion}
      phaseCountdown={phaseCountdown}
      game={game}
      players={_get(game, 'players', [])}
    />
  );

  const renderFlashingSection = () => (
    <FlashNumber number={numbers[currentNumberIndex]} />
  );

  const renderAnsweringSection = () => (
    <View style={[styles.answerContainer, !isCompactMode && { width: '40%' }]}>
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          maxHeight: 400,
        }}
      >
        <Text style={styles.countdown}>
          {currentQuestion < noOfQuestions ? 'Next Question in' : 'Results in '}
        </Text>
        <Text style={styles.countdownTime}>{phaseCountdown}</Text>
      </View>
      <View style={styles.textInputRow}>
        <CustomTextInput {...inputProps} />
      </View>
    </View>
  );

  const renderGameContent = () => {
    switch (gamePhase) {
      case FLASH_ANZAN_GAME_PHASES.CONFIG:
        return renderConfigSelection();
      case FLASH_ANZAN_GAME_PHASES.STARTING:
        return renderStartingCountdown();
      case FLASH_ANZAN_GAME_PHASES.FLASHING:
        return renderFlashingSection();
      case FLASH_ANZAN_GAME_PHASES.ANSWERING:
        return renderAnsweringSection();
      default:
        return null;
    }
  };

  if (gamePhase === FLASH_ANZAN_GAME_PHASES.COMPLETED) {
    return (
      <Redirect
        href={`/explore/game-result?gameType=${gameType}&feature=${EXPLORE_FEATURES.FLASH_ANZAN}`}
      />
    );
  }

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.innerContainer,
          (isFlashing || !isMobileBrowser) && { justifyContent: 'center' },
        ]}
      >
        {renderGameContent()}
      </View>
    </View>
  );
};

export default React.memo(PlayDummyFlashAnzanDuelGame);
