import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import { useLocalSearchPara<PERSON>, useRouter } from 'expo-router';
import useExploreGameStore from 'modules/activation/store/useExploreGameStore';
import victoryBg from 'assets/images/game/victoryGradient.png';
import GameResultPlayers from 'modules/game/pages/GameResultPage/components/GameResultPlayers';
import MetricsCard from 'shared/MetricsCard';
import RatingIcon from 'assets/images/game/ratingIconWhite.png';
import StatikCoinsIcon from 'assets/images/game/statikCoinWhite.png';
import useExploreGameResult from 'modules/activation/hooks/useExploreGameResult';
import useMediaQuery from 'core/hooks/useMediaQuery';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import {
  CopilotProvider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import CustomTooltip, {
  withCustomProps,
} from 'modules/activation/components/CustomWalkthroughTooltip';
import ActivationProgress from 'modules/activation/components/ActivationProgress';
import {
  FINISHED_WALKTHROUGH_MODAL_INFO_BY_FEATURE,
  WALKTHROUGH_STEPS_INFO_BY_FEATURE,
} from 'modules/activation/constants/explore';
import FinishedWalkthroughModal from 'modules/activation/components/FinishedWalkthroughModal';
import styles from './ExploreGameResult.style';
import GameResultHeader from './components/ExploreGameResultHeader';

const WalkthroughableView = walkthroughable(View);

const ExploreGameResult: React.FC = () => {
  const router = useRouter();
  const { gameType, feature }: { gameType: string; feature: string } =
    useLocalSearchParams();

  const STEPS_INFO = WALKTHROUGH_STEPS_INFO_BY_FEATURE[feature] ?? EMPTY_OBJECT;

  const TOTAL_STEPS = STEPS_INFO.totalSteps;

  const INITIAL_STEP = STEPS_INFO.preDummyPlay;

  const [currentStep, setCurrentStep] = useState(INITIAL_STEP + 1);

  const [hasFinishedWalkthrough, setHasFinishedWalkthrough] = useState(false);

  const onFinishedModalClose = useCallback(() => {
    setHasFinishedWalkthrough(false);
  }, []);

  const FINISHED_WALKTHROUGH_MODAL_INFO =
    FINISHED_WALKTHROUGH_MODAL_INFO_BY_FEATURE[feature];

  const { isMobile } = useMediaQuery();

  const { currentGame, isGameCompleted, userBestScore } = useExploreGameStore();

  const { start, copilotEvents } = useCopilot();

  const {
    isCurrentPlayerWinner,
    isFlashAnzan,
    adaptedPlayers,
    player1,
    player2,
    currentPlayerOriginalRating,
    currentPlayer,
    currentPlayerOriginalStatikCoins,
    navigateToNewGame,
  } = useExploreGameResult({
    game: currentGame,
  });

  useEffect(() => {
    if (!currentGame && !isGameCompleted) {
      router.replace('/home');
    }
  }, [currentGame, isGameCompleted, router]);

  const goToHome = useCallback(() => {
    router.navigate('/home');
  }, [router]);

  const navigateToHome = useCallback(() => {
    router.replace('/home');
  }, [router]);

  const onClickStep = useCallback((index: number) => {
    // if (index >= 1 && index <= TOTAL_STEPS) {
    //   setCurrentStep(index);
    // }
  }, []);

  const startWalkthroughRef = useRef(start);
  startWalkthroughRef.current = start;

  useEffect(() => {
    setTimeout(() => startWalkthroughRef.current(), 1000);
  }, []);

  useEffect(() => {
    copilotEvents.on('stepChange', (step) => {
      setCurrentStep(step ? INITIAL_STEP + step.order : 0);
    });

    copilotEvents.on('stop', () => {
      setHasFinishedWalkthrough(true);
    });
  }, [copilotEvents]);

  if (!currentGame) {
    return (
      <View style={styles.container}>
        <Text>Loading results...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.mainContainer}>
      <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
        <CopilotStep text="" order={1} name="Result Screen">
          <WalkthroughableView
            style={{
              position: 'absolute',
            }}
          />
        </CopilotStep>
        <View style={[styles.container]}>
          <Image source={victoryBg} style={styles.gradientBg} />
          <GameResultHeader game={currentGame} />
          <GameResultPlayers
            adaptedPlayers={adaptedPlayers}
            player1={player1}
            player2={player2}
            isCurrPlayerWinner={isCurrentPlayerWinner}
            gameType={gameType}
            isFlashAnzan={isFlashAnzan}
          />
          <View style={styles.metricsContainer}>
            <CopilotStep
              text={
                'win = Rating increase\nLoss = Rating Decrease\n\nhigher the rating, tougher the questions'
              }
              order={2}
              name="Rating System"
            >
              <WalkthroughableView>
                <MetricsCard
                  title="RATING"
                  value={currentPlayerOriginalRating || 0}
                  changeValue={(currentPlayer as any)?.ratingChange || 0}
                  imageSource={RatingIcon}
                  containerStyle={isMobile && { maxWidth: 200 }}
                />
              </WalkthroughableView>
            </CopilotStep>
            <CopilotStep
              text={
                'Xp is earned after each game\n\n Your total XP shows your overall game experience'
              }
              order={3}
              name="XP System"
            >
              <WalkthroughableView>
                <MetricsCard
                  title="TOTAL XP"
                  value={currentPlayerOriginalStatikCoins || 0}
                  changeValue={(currentPlayer as any)?.statikCoinsEarned || 0}
                  imageSource={StatikCoinsIcon}
                  containerStyle={isMobile && { maxWidth: 200 }}
                />
              </WalkthroughableView>
            </CopilotStep>
          </View>

          <View style={styles.footerContainer}>
            <InteractiveSecondaryButton
              label="NEW GAME"
              labelStyle={styles.bottomButtonLabel}
              buttonStyle={styles.bottomButtonStyle}
              buttonContainerStyle={{ width: '100%' }}
              onPress={navigateToNewGame}
              borderColor={dark.colors.secondary}
            />
          </View>
        </View>
      </View>
      <FinishedWalkthroughModal
        isVisible={hasFinishedWalkthrough}
        onFinished={navigateToHome}
        key="finishedWalkthroughModal"
        finishedConfig={FINISHED_WALKTHROUGH_MODAL_INFO}
        onBackdropPress={onFinishedModalClose}
      />
      <Animatable.View
        animation="slideInDown"
        duration={500}
        delay={500}
        style={[StyleSheet.absoluteFill, { zIndex: 1 }]}
      >
        <ActivationProgress
          currentStep={currentStep}
          stepsCount={TOTAL_STEPS}
          onClose={goToHome}
          onClickStep={onClickStep}
        />
      </Animatable.View>
    </SafeAreaView>
  );
};

const MARGIN = 16;
const WIDTH = Dimensions.get('window').width - 2 * MARGIN;

const BlitzGameTooltip = withCustomProps(CustomTooltip, {
  color: dark.colors.blitzGameColor,
});

const ExploreGameResultContainer = React.forwardRef((props, ref) => (
  <CopilotProvider
    tooltipComponent={BlitzGameTooltip as any}
    tooltipStyle={{
      backgroundColor: 'transparent',
      padding: 0,
      borderRadius: 0,
      width: WIDTH,
      maxWidth: WIDTH,
      left: MARGIN,
    }}
    arrowColor="transparent"
    stopOnOutsideClick
    androidStatusBarVisible
    stepNumberComponent={() => null} // Hide step numbers
    backdropColor="rgba(0, 0, 0, 0.6)"
    labels={{
      previous: 'Previous',
      next: 'Next',
      skip: 'Skip',
      finish: 'Got it!',
    }}
  >
    <ExploreGameResult {...props} />
  </CopilotProvider>
));

export default React.memo(ExploreGameResultContainer);
