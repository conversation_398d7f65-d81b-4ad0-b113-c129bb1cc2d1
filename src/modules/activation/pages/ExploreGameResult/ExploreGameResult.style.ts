import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

export const CARD_HEIGHT = 704;
export const MAX_CARD_WIDTH = 420;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: dark.colors.background,
    overflow: 'hidden',
  },
  container: {
    margin: 0,
    maxHeight: CARD_HEIGHT,
    maxWidth: MAX_CARD_WIDTH,
    padding: 16,
    margin: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 24,
    borderColor: dark.colors.tertiary,
    backgroundColor: dark.colors.gradientBackground,
  },
  captureViewContainer: {
    maxWidth: 418,
    width: '100%',
    flexDirection: 'column',
  },
  mobileBrowserContentContainer: {
    marginTop: 40,
  },
  gradientCardContainer: {
    maxWidth: 420,
    width: '100%',
    flexDirection: 'column',
  },
  cardContainer: {
    backgroundColor: dark.colors.baccardContainerkground,
    maxHeight: CARD_HEIGHT,
    height: 460,
    width: 360,
    maxWidth: MAX_CARD_WIDTH,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: dark.colors.tertiary,
    padding: 18,
    gap: 24,
    paddingBottom: 24,
    borderRadius: 18,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 8,
    marginTop: 10,
    marginBottom: 8,
  },
  gradientBg: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },

  // footer actions
  footerContainer: {
    height: 60,
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 10,
    flexDirection: 'row',
  },
  bottomButtonLabel: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: 'white',
    alignSelf: 'center',
    opacity: 0.9,
  },
  bottomButtonStyle: {
    flex: 1,
    backgroundColor: dark.colors.card,
    paddingHorizontal: 8,
    minWidth: 150,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: dark.colors.secondary,
    borderWidth: 0.5,
    borderRadius: 12,
  },
  bottomButtonBackgroundStyle: {
    flex: 1,
    minWidth: 140,
    backgroundColor: dark.colors.secondary,
    opacity: 0.2,
    borderRadius: 12,
  },
});

export default styles;
