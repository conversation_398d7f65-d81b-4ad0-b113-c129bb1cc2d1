import { View } from 'react-native';
import React from 'react';
import * as Animatable from 'react-native-animatable';
import styles from './ModuleActivationScreen.style';

const ModuleActivationScreen = ({ moduleInfo, moduleProgress }) => (
  <View style={styles.detailsContainer}>
    <View style={styles.detailsContent}>
      <View style={[styles.moduleIconContainer]}>
        <Animatable.Image
          source={{ uri: moduleInfo?.tutorialImageIcon }}
          style={styles.infoIcon}
          resizeMode="contain"
          animation="fadeInUp"
          delay={130}
        />

        <Animatable.Text
          style={styles.moduleIcon}
          animation="fadeInUp"
          delay={150}
        >
          TUTORIALS
        </Animatable.Text>
      </View>
      <Animatable.Text
        style={styles.moduleTitle}
        animation="fadeInUp"
        delay={170}
      >
        {moduleInfo?.title}
      </Animatable.Text>
      <Animatable.Text
        style={styles.moduleDescription}
        animation="fadeInUp"
        delay={200}
      >
        {moduleInfo?.description}
      </Animatable.Text>
    </View>
  </View>
);
export default React.memo(ModuleActivationScreen);
