import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  // Module Details Screen
  detailsContainer: {
    flex: 1,
    backgroundColor: dark.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailsContent: {
    gap: 28,
    alignItems: 'center',
    marginBottom: 200,
  },
  moduleIconContainer: {
    gap: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moduleIcon: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    lineHeight: 16,
    color: withOpacity(dark.colors.text, 0.4),
  },
  infoIcon: {
    width: 39,
    height: 39,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  moduleTitle: {
    fontSize: 24,
    color: dark.colors.text,
    fontFamily: 'Montserrat-600',
  },
  moduleDescription: {
    fontSize: 12,
    color: withOpacity(dark.colors.text, 0.6),
    textAlign: 'center',
    lineHeight: 16,
    fontFamily: 'Montserrat-500',
    textTransform: 'uppercase',
  },
  featuresContainer: {
    marginTop: 'auto',
  },
  featuresLabel: {
    fontSize: 12,
    color: dark.colors.text,
    marginBottom: 8,
  },

  ipeHint: {
    fontSize: 14,
    color: dark.colors.placeholder,
    textAlign: 'center',
    marginTop: 20,
    fontStyle: 'italic',
  },

  // Common Progress Styles
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: dark.colors.text,
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: dark.colors.textDark,
    marginBottom: 12,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: dark.colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },

  // Common Button Styles
  homeButton: {
    width: '100%',
    maxWidth: 300,
  },
});

export default styles;
