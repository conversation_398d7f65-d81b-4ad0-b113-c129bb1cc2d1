import { Dimensions, StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const { width: screenWidth } = Dimensions.get('window');

const SIZES = {
  padding: 16,
  radius: 8,
  h1: 32,
  h2: 24,
  h3: 20,
  h4: 16,
  body1: 18,
  body2: 16,
  body2_5: 14,
  body3: 12,
  body4: 10,
};

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
    justifyContent: 'center',
  },
  infoContainer: {
    paddingTop: 16,
    gap: 70,
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: SIZES.padding * 1.5,
    marginBottom: SIZES.padding * 2,
  },
  backButton: {
    padding: SIZES.padding / 2,
    marginRight: SIZES.padding,
  },
  backArrowText: {
    color: dark.colors.text,
    fontSize: SIZES.h1 * 1.5,
    fontWeight: 'bold',
  },
  titleContainer: {
    alignItems: 'center',
    gap: 24,
    justifyContent: 'center',
    height: 132,
  },
  mainTitle: {
    fontSize: 24,
    fontFamily: 'Montserrat-600',
    color: dark.colors.text,
    textAlign: 'center',
  },
  subTitle: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: withOpacity(dark.colors.text, 0.6),
    paddingHorizontal: 35,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  blitzTypeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 4,
    height: 20,
  },
  blitzTypeText: {
    fontSize: 12,
    color: withOpacity(dark.colors.text, 0.6),
    fontFamily: 'Montserrat-600',
    letterSpacing: 1,
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  carouselContainer: {
    alignItems: 'center',
  },
  carouselItemView: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 204,
    height: 220,
    overflow: 'hidden',
  },
  carouselItemContainer: {
    justifyContent: 'center',
    height: '100%',
    alignItems: 'center',
    width: '100%',
  },
  footerSection: {
    paddingHorizontal: 16,
    alignItems: 'center',
    width: '100%',
    height: 100,
    position: 'absolute',
    bottom: 120,
  },
  learnButton: {
    width: '100%',
    height: 52,
    marginBottom: SIZES.padding * 1.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  learnButtonLabel: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    color: dark.colors.textLight,
    textTransform: 'uppercase',
  },
  skipAllButton: {
    paddingVertical: SIZES.padding / 2,
  },
  skipAllText: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    color: withOpacity(dark.colors.text, 0.4),
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  swipeHintContainer: {
    position: 'absolute',
    bottom: 20,
    width: '100%',
    alignItems: 'center',
  },
  swipeHintText: {
    color: withOpacity(dark.colors.text, 0.6),
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  featuresContainer: { flex: 1, backgroundColor: '#fff', padding: 10 },
  featuresHeader: { alignItems: 'center', marginBottom: 20, paddingTop: 20 },
  featuresTitle: {
    fontSize: 24,
    fontFamily: 'Montserrat-600',
    color: dark.colors.tertiary,
    marginBottom: 5,
  },
  featuresSubtitle: { fontSize: 16, color: '#666' },
  featuresList: { marginBottom: 20 },
  featureItem: {
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    padding: 15,
    marginRight: 10,
    width: screenWidth * 0.7,
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureIconContainer: {
    marginRight: 10,
    padding: 10,
    backgroundColor: '#ddd',
    borderRadius: 25,
  },
  featureIcon: { fontSize: 24 },
  featureContent: { flex: 1 },
  featureTitle: { fontSize: 18, fontFamily: 'Montserrat-600', color: '#333' },
  featureDescription: { fontSize: 14, color: '#555', marginTop: 4 },
  featureStatus: { marginLeft: 10 },
  newBadge: {
    fontSize: 12,
    color: 'white',
    backgroundColor: 'green',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 5,
    overflow: 'hidden',
  },
  featuresFooter: { alignItems: 'center', paddingBottom: 20 },
  homeButton: { width: '80%' },
  indicator: {
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicatorText: {
    color: withOpacity(dark.colors.textLight, 0.6),
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
});
