import React, { useCallback, useRef, useState } from 'react';
import {
  Dimensions,
  Image,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import GestureRecognizer from 'react-native-swipe-gestures';
import Carousel from 'react-native-reanimated-carousel';
import { router, useLocalSearchParams } from 'expo-router';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from '@/src/core/constants/themes/dark';
import {
  EXPLORE_MODULE_TITLE,
  FEATURE_ACTIVATION_INFO,
} from 'modules/activation/constants/explore';
import { stringifyQueryParams } from 'core/utils/general';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import { withOpacity } from 'core/utils/colorUtils';
import styles from './FeatureActivationScreen.style';

const { width: screenWidth } = Dimensions.get('window');

interface FeatureItem {
  id: string | number;
  title?: string;
}

interface FeatureActivationScreenProps {
  featuresToExplore: FeatureItem[];
}

const FeatureActivationScreen: React.FC<FeatureActivationScreenProps> = ({
  featuresToExplore,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showFooter, setShowFooter] = useState(false);

  const setShowFooterToTrue = useCallback(() => {
    setShowFooter(true);
  }, []);

  const searchParams = useLocalSearchParams();
  const module = (searchParams.module as string) || '';

  const carouselRef = useRef(null);

  const handleSkipAll = useCallback(() => {
    router.navigate('/explore');
  }, []);

  const parallaxScrollingOffset = 204;

  const infoOfCurrentFeature =
    FEATURE_ACTIVATION_INFO[featuresToExplore[currentIndex] as any];

  const onPressFeature = useCallback(() => {
    const feature = featuresToExplore[currentIndex];
    const stingifiedQueryParams = stringifyQueryParams({ feature });
    router.navigate(`/explore/walkthrough?${stingifiedQueryParams}`);
  }, [currentIndex, featuresToExplore]);

  const renderCarouselItem = ({
    item,
    index,
  }: {
    item: FeatureItem;
    index: number;
  }) => {
    const featureInfo =
      FEATURE_ACTIVATION_INFO[featuresToExplore[index] as any];

    return (
      <TouchableOpacity
        key={featureInfo?.title}
        style={[styles.carouselItemContainer]}
        onPress={onPressFeature}
      >
        <Image
          source={{ uri: featureInfo?.imageUrl }}
          style={styles.carouselItemView}
          resizeMode="contain"
        />
      </TouchableOpacity>
    );
  };

  const moduleTitle = EXPLORE_MODULE_TITLE[module];

  return (
    <GestureRecognizer onSwipeUp={setShowFooterToTrue} style={styles.container}>
      <Animatable.View
        style={styles.infoContainer}
        delay={200}
        animation="slideInUp"
      >
        <View style={styles.titleContainer}>
          <Animatable.View
            style={styles.blitzTypeContainer}
            delay={200}
            animation="fadeInUp"
          >
            {currentIndex === 0 && (
              <Text style={styles.blitzTypeText}>TYPES OF {moduleTitle}</Text>
            )}
          </Animatable.View>
          <Animatable.Text
            style={styles.mainTitle}
            delay={200}
            animation="fadeInUp"
          >
            {infoOfCurrentFeature?.title}
          </Animatable.Text>
          <Animatable.Text
            delay={200}
            animation="fadeInUp"
            style={styles.subTitle}
          >
            {infoOfCurrentFeature?.description}
          </Animatable.Text>
        </View>

        <Animatable.View
          style={styles.carouselContainer}
          animation="fadeInUp"
          delay={200}
        >
          <Carousel
            ref={carouselRef}
            loop={false}
            width={screenWidth}
            height={220}
            autoPlay={false}
            data={featuresToExplore}
            scrollAnimationDuration={1000}
            onSnapToItem={(index) => {
              setCurrentIndex(index);
            }}
            renderItem={renderCarouselItem}
            mode="parallax"
            modeConfig={{
              parallaxScrollingScale: 0.95,
              parallaxScrollingOffset,
              parallaxAdjacentItemScale: 0.5,
            }}
          />
        </Animatable.View>

        {showFooter && (
          <Animatable.View animation="slideInUp" style={styles.footerSection}>
            <InteractiveSecondaryButton
              label="GET A WALKTHROUGH"
              onPress={onPressFeature}
              buttonContainerStyle={styles.learnButton}
              labelStyle={styles.learnButtonLabel}
              borderColor={dark.colors.victoryColor}
              buttonStyle={{
                borderRadius: 20,
              }}
              borderComponentStyle={{
                borderBottomLeftRadius: 24,
                borderBottomRightRadius: 24,
                width: '99%',
                justifyContent: 'center',
                alignItems: 'center',
                bottom: -4,
              }}
            />
            <Pressable onPress={handleSkipAll} style={styles.skipAllButton}>
              <Text style={styles.skipAllText}>SKIP ALL</Text>
            </Pressable>
          </Animatable.View>
        )}
        {!showFooter && (
          <Animatable.View animation="slideInUp" style={styles.footerSection}>
            <Pressable onPress={setShowFooterToTrue} style={styles.indicator}>
              <Icon
                type={ICON_TYPES.IONICON}
                name="chevron-up"
                size={16}
                color={withOpacity(dark.colors.textLight, 0.4)}
              />
              <Text style={styles.indicatorText}>Swipe up</Text>
            </Pressable>
          </Animatable.View>
        )}
      </Animatable.View>
    </GestureRecognizer>
  );
};

export default React.memo(FeatureActivationScreen);
