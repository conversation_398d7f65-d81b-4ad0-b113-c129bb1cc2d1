import React from 'react';
import { Text, View } from 'react-native';
import ExploreMatiks from 'modules/activation/components/ExploreMatiks';
import styles from './ExploreFeaturesHomePage.style';

const ExploreFeaturesHomePage = () => (
  <View style={styles.container}>
    <View style={styles.headerContainer}>
      {/* <Text style={styles.titleText}>EXPLORE FEATURES</Text> */}
    </View>
    <ExploreMatiks />
    <View style={styles.contentContainer}>
      <Text style={styles.titleText}>DUELS</Text>
    </View>
  </View>
);

export default React.memo(ExploreFeaturesHomePage);
