import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Dimensions, SafeAreaView, View } from 'react-native';

import ActivationProgress from 'modules/activation/components/ActivationProgress';
import useGoBack from 'navigator/hooks/useGoBack';
import {
  CopilotProvider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import StartActivationModal from 'modules/activation/components/StartActivationModal';
import dark from 'core/constants/themes/dark';
import {
  ACTIVATION_GAME_CONFIG,
  EXPLORE_FEATURES,
  FEATURE_ACTIVATION_INFOS,
  WALKTHROUGH_STEPS_INFO_BY_FEATURE,
} from 'modules/activation/constants/explore';
import { router } from 'expo-router';
import _map from 'lodash/map';
import useExplorationStatusFromStore from 'store/useExploredFeaturesStore/useExplorationStatusFromStore';
import styles from './ExploreFlashAnzanGame.style';
import CustomTooltip, {
  withCustomProps,
} from '../../components/CustomWalkthroughTooltip';
import PlayFirstGameModal from '../../components/PlayFirstGameModal';
import {
  FLASH_ANZAN_ANSWERING_STEPS,
  FLASH_ANZAN_CONFIG_STEPS,
  FLASH_ANZAN_FLASHING_STEPS,
  FLASH_ANZAN_FOOTER_STEP,
  WALKTHROUGH_PHASES,
} from '../../constants/flashAnzanWalkthrough';

const FlashAnzanTooltip = withCustomProps(CustomTooltip, {
  color: dark.colors.memoryGameColor,
});

const WalkthroughableView = walkthroughable(View);

const ExploreFlashAnzanGame = React.memo(
  ({
    feature,
    walkthroughPhase,
    setWalkthroughPhase,
    currentCopilotStep,
    setCurrentCopilotStep,
  }: any) => {
    const { goBack } = useGoBack();
    const { markFeatureAsExplored } = useExplorationStatusFromStore();

    const [playFirstGameModalVisible, setPlayFirstGameModalVisible] =
      useState(false);
    const [startActivationModalVisible, setStartActivationModalVisible] =
      useState(walkthroughPhase === WALKTHROUGH_PHASES.CONFIG);

    const GAME_ACTIVATION_INFO = FEATURE_ACTIVATION_INFOS[feature] as any;

    const STEPS_INFO = WALKTHROUGH_STEPS_INFO_BY_FEATURE[feature];

    const TOTAL_STEPS = STEPS_INFO.totalSteps;

    const INITIAL_STEP = STEPS_INFO.preDummyPlay;

    const gameConfig = ACTIVATION_GAME_CONFIG[feature] as any;

    const { start, copilotEvents } = useCopilot();

    const onClickStep = useCallback(
      (index: number) => {
        // if (index >= 1 && index <= TOTAL_STEPS) {
        //   setCurrentCopilotStep(index);
        // }
      },
      [setCurrentCopilotStep],
    );

    const setCurrentCopilotStepRef = useRef(setCurrentCopilotStep);
    setCurrentCopilotStepRef.current = setCurrentCopilotStep;

    const onPressSkip = useCallback(async () => {
      setStartActivationModalVisible(false);
      goBack();
      await markFeatureAsExplored(feature);
      // TODO: @mohan mark feature explored
    }, [feature, goBack, markFeatureAsExplored]);

    const onPressStartActivation = useCallback(() => {
      setStartActivationModalVisible(false);
      start();
    }, [start]);

    const onPressStartFlashAnzanGame = useCallback(async () => {
      setPlayFirstGameModalVisible(false);
      markFeatureAsExplored(feature);
      router.push(
        `/explore/game-play?gameType=${gameConfig?.gameType}&feature=${EXPLORE_FEATURES.FLASH_ANZAN}`,
      );
    }, [feature, gameConfig?.gameType, markFeatureAsExplored]);

    const handleStop = useCallback(() => {
      if (walkthroughPhase === WALKTHROUGH_PHASES.CONFIG) {
        setWalkthroughPhase(WALKTHROUGH_PHASES.FLASHING);
      } else if (walkthroughPhase === WALKTHROUGH_PHASES.FLASHING) {
        setWalkthroughPhase(WALKTHROUGH_PHASES.ANSWERING);
      } else {
        setPlayFirstGameModalVisible(true);
      }
    }, [walkthroughPhase, setWalkthroughPhase, setPlayFirstGameModalVisible]);

    const handleStopRef = useRef(handleStop);
    handleStopRef.current = handleStop;

    useEffect(() => {
      copilotEvents.on('stop', handleStopRef.current);

      copilotEvents.on('stepChange', (step) => {
        setCurrentCopilotStepRef.current(step ? step.order : 0);
      });

      return () => {
        copilotEvents.off('stop', handleStopRef.current);
      };
    }, [copilotEvents]);

    useEffect(() => {
      if (
        walkthroughPhase !== WALKTHROUGH_PHASES.CONFIG &&
        currentCopilotStep < INITIAL_STEP - 1
      ) {
        start();
      }
    }, [walkthroughPhase, start, currentCopilotStep]);

    const renderWalkthroughContent = () => {
      let steps: any[] = [];
      if (walkthroughPhase === WALKTHROUGH_PHASES.CONFIG) {
        steps = FLASH_ANZAN_CONFIG_STEPS;
      } else if (walkthroughPhase === WALKTHROUGH_PHASES.FLASHING) {
        steps = FLASH_ANZAN_FLASHING_STEPS;
      } else if (walkthroughPhase === WALKTHROUGH_PHASES.ANSWERING) {
        steps = FLASH_ANZAN_ANSWERING_STEPS;
      }

      return _map(steps, (step) => {
        const WalkThroughComponent = step.component;
        const { name, text, order } = step ?? {};

        return (
          <CopilotStep key={name} text={text} order={order} name={name}>
            <WalkthroughableView>
              <WalkThroughComponent />
            </WalkthroughableView>
          </CopilotStep>
        );
      });
    };

    const renderAnsweringFooter = () => {
      if (walkthroughPhase !== WALKTHROUGH_PHASES.ANSWERING) {
        return null;
      }
      const footerStep = FLASH_ANZAN_FOOTER_STEP;
      const { name, text, order } = footerStep;
      const WalkThroughComponent = footerStep.component;

      return (
        <CopilotStep key={name} text={text} order={order} name={name}>
          <WalkthroughableView>
            <WalkThroughComponent />
          </WalkthroughableView>
        </CopilotStep>
      );
    };

    return (
      <SafeAreaView style={styles.container}>
        <ActivationProgress
          currentStep={currentCopilotStep}
          stepsCount={TOTAL_STEPS}
          onClose={goBack}
          onClickStep={onClickStep}
        />

        <View style={{ flex: 1, justifyContent: 'center' }}>
          {renderWalkthroughContent()}
        </View>

        {renderAnsweringFooter()}

        <StartActivationModal
          onSkip={onPressSkip}
          activationConfig={GAME_ACTIVATION_INFO}
          onStart={onPressStartActivation}
          isVisible={startActivationModalVisible}
        />
        <PlayFirstGameModal
          isVisible={playFirstGameModalVisible}
          onClickPlayNow={onPressStartFlashAnzanGame}
          gameConfig={gameConfig}
        />
      </SafeAreaView>
    );
  },
);

const MARGIN = 16;
const WIDTH = Dimensions.get('window').width - 2 * MARGIN;

const ExploreFlashAnzanGameContainer = (props: any) => {
  const [walkthroughPhase, setWalkthroughPhase] = useState(
    WALKTHROUGH_PHASES.CONFIG,
  );
  const [currentCopilotStep, setCurrentCopilotStep] = useState(1);

  const { feature }: { feature: string } = props ?? EMPTY_OBJECT;
  const STEPS_INFO = WALKTHROUGH_STEPS_INFO_BY_FEATURE[feature];

  const TOTAL_STEPS = STEPS_INFO.totalSteps;

  const labels = {
    prev: '',
    next: 'Next',
    skip: 'Skip',
    finish: currentCopilotStep === TOTAL_STEPS ? 'Got it!' : 'Next',
  };

  return (
    <CopilotProvider
      key={walkthroughPhase}
      tooltipComponent={FlashAnzanTooltip as any}
      tooltipStyle={{
        backgroundColor: 'transparent',
        padding: 0,
        borderRadius: 0,
        width: WIDTH,
        maxWidth: WIDTH,
        left: MARGIN,
      }}
      arrowColor="transparent"
      stopOnOutsideClick
      androidStatusBarVisible
      stepNumberComponent={() => null} // Hide step numbers
      backdropColor="rgba(0, 0, 0, 0.6)"
      labels={labels}
    >
      <ExploreFlashAnzanGame
        {...props}
        walkthroughPhase={walkthroughPhase}
        setWalkthroughPhase={setWalkthroughPhase}
        currentCopilotStep={currentCopilotStep}
        setCurrentCopilotStep={setCurrentCopilotStep}
      />
    </CopilotProvider>
  );
};

export default React.memo(ExploreFlashAnzanGameContainer);
