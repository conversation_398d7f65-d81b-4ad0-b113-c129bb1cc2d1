import { useMemo } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import _compact from 'lodash/compact';
import useGetFeaturedShowdown from 'modules/showdown/hooks/query/useGetFeaturedShowdown';
import { BANNER_TYPES } from 'core/constants/bannerTypes';
import useFetchFeaturedContests from '../../contest/hooks/useFetchFeaturedContests';

const useHasHomeBannerData = () => {
  const { featuredContests } = useFetchFeaturedContests();
  const { featuredShowdowns } = useGetFeaturedShowdown();

  const toShowContestBanner = !_isEmpty(featuredContests);
  const featuredShowdown = _get(featuredShowdowns, 0);
  const toShowShowdownBanner = !_isEmpty(featuredShowdown);

  const bannersListWithData = useMemo(
    () =>
      _compact([
        toShowContestBanner && {
          data: featuredContests[0],
          type: BANNER_TYPES.CONTEST_BANNER,
        },
        toShowShowdownBanner && {
          data: featuredShowdown,
          type: BANNER_TYPES.SHOWDOWN_BANNER,
        },
      ]),
    [
      featuredContests,
      featuredShowdown,
      toShowContestBanner,
      toShowShowdownBanner,
    ],
  );

  return !_isEmpty(bannersListWithData);
};

export default useHasHomeBannerData;
