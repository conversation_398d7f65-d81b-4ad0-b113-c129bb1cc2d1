import React from 'react';
import { Pressable, Text, View } from 'react-native';

import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import { ReferralErrorViewProps } from '../types';

const ReferralErrorView: React.FC<ReferralErrorViewProps> = ({ styles, onSkip, onReenter }) => {
  return (
    <View style={[styles.footerError, styles.footer]}>
      <View style={{ width: '50%' }}>
        <Pressable
          style={styles.skipForNowButton}
          onPress={onSkip}
        >
          <Text style={styles.skipForNow}>SKIP FOR NOW</Text>
        </Pressable>
      </View>
      <View style={{ width: '50%' }}>
        <InteractivePrimaryButton
          onPress={onReenter}
          label="RE-ENTER"
          buttonContainerStyle={{ height: 51 }}
          buttonStyle={[styles.continueButton]}
          labelStyle={styles.continueButtonText}
          buttonBorderBackgroundStyle={styles.continueBackground}
        />
      </View>
    </View>
  );
};

export default ReferralErrorView;
