import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      marginBottom: 20,
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: 16,
    },
    title: {
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      letterSpacing: 1,
      marginBottom: 20,
      color: dark.colors.textLight,
      textAlign: 'center',
    },
    cardContainer: {
      flexDirection: 'row',
      width: '100%',
      // maxWidth: 320,
      gap: 12,
    },
    userCard: {
      backgroundColor: dark.colors.primary,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: dark.colors.tertiary,
      padding: 16,
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    avatarSection: {
      marginRight: 12,
    },
    avatarContainer: {
      position: 'relative',
      alignItems: 'center',
    },
    userImage: {
      borderWidth: 2,
      borderColor: '#ED7648',
      borderRadius: 60,
      padding: 4,
    },
    userDetails: {
      flex: 1,
    },
    userName: {
      fontSize: 16,
      fontFamily: 'Montserrat-600',
      color: dark.colors.textLight,
      marginBottom: 2,
    },
    userMeta: {
      fontSize: 12,
      fontFamily: 'Montserrat-600',
      color: dark.colors.textLight,
      opacity: 0.4,
    },
    badgeContainer: {
      position: 'absolute',
      bottom: -8,
      backgroundColor: dark.colors.card,
      borderRadius: 20,
      paddingHorizontal: 6,
      paddingVertical: 4,
      borderWidth: 1,
      borderColor: '#ED7648',
    },
    badgeText: {
      fontSize: 10,
      fontFamily: 'Montserrat-800',
      color: '#FFFFFF',
      letterSpacing: 0.5,
    },
    addButtonCard: {
      borderRadius: 20,
      backgroundColor: dark.colors.primary,
      borderWidth: 1,
      borderColor: dark.colors.tertiary,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 20
    },
    disabledButton: {
      opacity: 0.5,
    },
  });

export const useReferralFromCardStyles = () => {
  const { isMobile } = useMediaQuery();
  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};
