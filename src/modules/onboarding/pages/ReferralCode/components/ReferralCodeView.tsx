import React from 'react';
import {
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  Text,
  View,
} from 'react-native';

import BackgroundImage from '@/assets/images/backgrounds/background_blocks.png';
import InitialChoiceView from './InitialChoiceView';
import ReferralInputView from './ReferralInputView';
import ReferralErrorView from './ReferralErrorView';
import ReferralActionsView from './ReferralActionsView';
import ReferralFromCard from './ReferralFromCard';
import { ReferralCodeViewProps } from '../types';

const ReferralCodeView: React.FC<ReferralCodeViewProps> = ({
  styles,
  referralState,
  keyboardState,
}) => {
  const {
    showReferralInput,
    referralCodeSubmissionError,
    isWrongReferralCode,
    shouldShowReferrerCard,
    referralCode,
    isValidReferralCode,
    referrerUser,
    sendFriendRequestSuccess,
    isSendingFriendRequest,
    handleSkipReferral,
    handleShowReferralInput,
    handleReferralCodeChange,
    handleSendFriendRequest,
    handleSubmitReferralCode,
    handleResetError,
  } = referralState;

  const { isKeyboardVisible, keyboardHeight } = keyboardState;

  const renderContent = () => {
    if (!showReferralInput) {
      return (
        <InitialChoiceView
          styles={styles}
          onNo={handleSkipReferral}
          onYes={handleShowReferralInput}
        />
      );
    }

    return (
      <ReferralInputView
        styles={styles}
        isValidReferralCode={isValidReferralCode}
        referralCode={referralCode}
        hasError={referralCodeSubmissionError || isWrongReferralCode}
        onReferralCodeChange={handleReferralCodeChange}
      />
    );
  };

  const renderReferrerCard = () => {
    if (!shouldShowReferrerCard) return null;

    return (
      <ReferralFromCard
        referrerInfo={referrerUser}
        onSendFriendRequest={handleSendFriendRequest}
        isSendingFriendRequest={isSendingFriendRequest}
        sendFriendRequestSuccess={sendFriendRequestSuccess}
      />
    );
  };

  const renderActions = () => {
    if (referralCodeSubmissionError) {
      return (
        <ReferralErrorView
          styles={styles}
          onSkip={handleSkipReferral}
          onReenter={handleResetError}
        />
      );
    }

    return (
      <ReferralActionsView
        styles={styles}
        showReferralInput={showReferralInput}
        isKeyboardVisible={isKeyboardVisible}
        isValidReferralCode={isValidReferralCode}
        keyboardHeight={keyboardHeight}
        sendFriendRequestSuccess={sendFriendRequestSuccess}
        onSkip={handleSkipReferral}
        onSubmit={handleSubmitReferralCode}
      />
    );
  };

  return (
    <>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
      >
        <ImageBackground source={BackgroundImage} style={[styles.image]}>
          <View style={styles.contentContainer}>
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                width: '100%',
              }}
            >
              <Text style={styles.title}>DO YOU HAVE A REFERRAL CODE ?</Text>
              {renderContent()}
            </View>
          </View>
        </ImageBackground>
      </KeyboardAvoidingView>
      {!isKeyboardVisible && renderReferrerCard()}
      {renderActions()}
    </>
  );
};

export default ReferralCodeView;
