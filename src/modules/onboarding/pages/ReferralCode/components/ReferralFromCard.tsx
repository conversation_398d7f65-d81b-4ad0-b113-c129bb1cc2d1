import Icon, { ICON_TYPES } from '@/src/components/atoms/Icon';
import { Image, Text, TouchableOpacity, View } from 'react-native';

import React from 'react';
import dark from '@/src/core/constants/themes/dark';
import { useReferralFromCardStyles } from './ReferralFromCard.style';
import { ReferralFromCardProps } from '../types';

const ReferralFromCard: React.FC<ReferralFromCardProps> = ({
  referrerInfo,
  onSendFriendRequest,
  isSendingFriendRequest,
  sendFriendRequestSuccess,
}) => {
  const styles = useReferralFromCardStyles();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>REFERRAL FROM</Text>

      <View style={styles.cardContainer}>
        <View style={styles.userCard}>
          <View style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              {/* <UserImage
                user={referrerInfo}
                size={60}
                rounded={true}
                style={styles.userImage}
              /> */}
              <Image
                source={{ uri: referrerInfo.profileImageUrl }}
                style={{
                  height: 52,
                  width: 52,
                  borderRadius: 52,
                  borderWidth: 2,
                  borderColor: '#ED7648',
                  padding: 4,
                }}
              />

              <View style={styles.badgeContainer}>
                <Text style={styles.badgeText} numberOfLines={1}>
                  {referrerInfo.badge}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.userDetails}>
            <Text style={styles.userName} numberOfLines={1}>
              {referrerInfo.name}
            </Text>
            <Text style={styles.userMeta} numberOfLines={1}>
              {referrerInfo.username}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[
            styles.addButtonCard,
            isSendingFriendRequest && styles.disabledButton,
          ]}
          onPress={onSendFriendRequest}
          disabled={isSendingFriendRequest || !onSendFriendRequest}
        >
          {sendFriendRequestSuccess ? (
            <Icon
              name="user-check"
              type={ICON_TYPES.FONT_AWESOME_5}
              size={25}
              color={dark.colors.success}
            />
          ) : (
            <Icon
              name="person-add-alt-1"
              type={ICON_TYPES.MATERIAL_ICONS}
              size={30}
              color={dark.colors.secondary}
            />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default React.memo(ReferralFromCard);
