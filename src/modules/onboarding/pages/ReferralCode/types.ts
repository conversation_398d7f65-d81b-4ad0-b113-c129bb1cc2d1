import { StyleProp, TextStyle, ViewStyle } from 'react-native';

// User types
export interface ReferrerUser {
  _id: string;
  name: string;
  username: string;
  profileImageUrl: string;
  badge: string;
}

// Hook return types
export interface UseReferralCodeStateReturn {
  // State
  referralCode: string | null;
  showReferralInput: boolean;
  hasError: boolean;
  referralCodeSubmissionError: boolean;
  isWrongReferralCode: boolean;
  sendFriendRequestSuccess: boolean;
  referrerUser: ReferrerUser | null;

  // Loading states
  submitLoading: boolean;
  fetchingUser: boolean;
  isSendingFriendRequest: boolean;

  // Computed values
  shouldShowReferrerCard: boolean;
  isReferralCodeEmpty: boolean;
  isValidReferralCode: boolean;

  // Actions
  handleSkipReferral: () => void;
  handleShowReferralInput: () => void;
  handleReferralCodeChange: (text: string) => void;
  handleSendFriendRequest: () => Promise<void>;
  handleSubmitReferralCode: () => Promise<void>;
  handleResetError: () => void;
}

export interface UseKeyboardHandlerReturn {
  isKeyboardVisible: boolean;
  keyboardHeight: number;
}

// Component prop types
export interface ReferralCodeViewProps {
  styles: any; // TODO: Type this properly based on the style object
  referralState: UseReferralCodeStateReturn;
  keyboardState: UseKeyboardHandlerReturn;
}

export interface InitialChoiceViewProps {
  styles: any;
  onNo: () => void;
  onYes: () => void;
}

export interface ReferralInputViewProps {
  styles: any;
  referralCode: string | null;
  hasError: boolean;
  onReferralCodeChange: (text: string) => void;
}

export interface ReferralErrorViewProps {
  styles: any;
  onSkip: () => void;
  onReenter: () => void;
}

export interface ReferralActionsViewProps {
  styles: any;
  showReferralInput: boolean;
  isKeyboardVisible: boolean;
  isValidReferralCode: boolean;
  keyboardHeight: number;
  isValidReferralCode: boolean;
  sendFriendRequestSuccess: boolean;
  onSkip: () => void;
  onSubmit: () => void;
}

export interface ReferralFromCardProps {
  referralCode: string;
  referrerInfo: ReferrerUser;
  onSendFriendRequest: () => void;
  isSendingFriendRequest: boolean;
  sendFriendRequestSuccess: boolean;
}

// Style types (basic structure - can be expanded)
export interface ReferralCodeStyles {
  mainContainer: StyleProp<ViewStyle>;
  header: StyleProp<ViewStyle>;
  contentContainer: StyleProp<ViewStyle>;
  title: StyleProp<TextStyle>;
  referralButtonContainer: StyleProp<ViewStyle>;
  referralButtonInnerContainer: StyleProp<ViewStyle>;
  referralButtonNoContainer: StyleProp<ViewStyle>;
  referralButtonYesContainer: StyleProp<ViewStyle>;
  referralButtonNoInnerContainer: StyleProp<ViewStyle>;
  continueButton: StyleProp<ViewStyle>;
  continueButtonText: StyleProp<TextStyle>;
  continueBackground: StyleProp<ViewStyle>;
  referralInputContainer: StyleProp<ViewStyle>;
  textInputStyle: StyleProp<TextStyle>;
  textInputBottomBorder: StyleProp<ViewStyle>;
  error: StyleProp<TextStyle>;
  footer: StyleProp<ViewStyle>;
  footerError: StyleProp<ViewStyle>;
  buttonContainer: StyleProp<ViewStyle>;
  buttonContainerStyle: StyleProp<ViewStyle>;
  skipButton: StyleProp<ViewStyle>;
  skipForNowButton: StyleProp<ViewStyle>;
  skipForNow: StyleProp<TextStyle>;
  noReferralCode: StyleProp<TextStyle>;
  image: StyleProp<any>;
}
