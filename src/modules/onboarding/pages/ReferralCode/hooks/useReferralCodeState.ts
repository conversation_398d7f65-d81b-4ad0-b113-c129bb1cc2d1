import { useCallback, useEffect, useState } from 'react';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { useRouter } from 'expo-router';
import { useSession } from 'modules/auth/containers/AuthProvider';
import Analytics from '@/src/core/analytics';
import { EVENTS } from '@/src/core/analytics/events/profile';

import { useReadReferralCode } from 'modules/onboarding/hooks/useReadReferralCode';
import useSendFriendRequest from 'modules/friendsAndFollowers/hooks/mutations/useSendFriendRequest';
import _debounce from 'lodash/debounce';
import _trim from 'lodash/trim';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import useSubmitReferralCode from '../../../hooks/useSubmitReferralCode';
import useGetUserByReferralCode from '../../../hooks/useGetUserByReferralCode';
import { UseReferralCodeStateReturn } from '../types';

const MIN_LENGTH_OF_USERNAME = 8;
const EMPTY_OBJECT = {};

const useReferralCodeState = (): UseReferralCodeStateReturn => {
  const { refreshCurrentUser } = useSession();
  const router = useRouter();

  // External hooks
  const { referralCode: referralCodeFromPlayStore } = useReadReferralCode();
  const {
    submitReferralCode,
    error: submitError,
    loading: submitLoading,
  } = useSubmitReferralCode();
  const {
    getUserByReferralCode,
    user: referrerUser,
    loading: fetchingUser,
  } = useGetUserByReferralCode();
  const { sendFriendRequest, isSendingFriendRequest } = useSendFriendRequest();

  // Local state
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [showReferralInput, setShowReferralInput] = useState<boolean>(false);
  const [referralCodeSubmissionError, setReferralCodeSubmissionError] =
    useState<boolean>(false);
  const [sendFriendRequestSuccess, setSendFriendRequestSuccess] =
    useState<boolean>(false);

  // Initialize referral code from play store
  useEffect(() => {
    if (referralCodeFromPlayStore) {
      setReferralCode(referralCodeFromPlayStore);
    }
  }, [referralCodeFromPlayStore]);

  // Actions
  const handleSkipReferral = useCallback(() => {
    router.replace('/home');
  }, [router]);

  const handleShowReferralInput = useCallback(() => {
    setShowReferralInput(true);
  }, []);

  const onChangeReferralCode = useCallback(
    _debounce((referralCode) => {
      getUserByReferralCode(_trim(referralCode));
    }, 300),
    [getUserByReferralCode],
  );

  const handleReferralCodeChange = useCallback(
    (text: string) => {
      setReferralCode(text);
      setReferralCodeSubmissionError(false);
      if (_size(text) >= MIN_LENGTH_OF_USERNAME) {
        onChangeReferralCode(text);
      }
    },
    [onChangeReferralCode],
  );

  const handleSendFriendRequest = useCallback(async () => {
    if (!referrerUser || isSendingFriendRequest) {
      return;
    }

    try {
      const response = await sendFriendRequest({
        receiverId: referrerUser._id,
      });

      if (response?.data?.sendFriendRequest) {
        setSendFriendRequestSuccess(true);
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: `Friend request sent to ${referrerUser.name || referrerUser.username}`,
        });
      } else {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: 'Failed to send friend request',
        });
      }
    } catch (error) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: 'Something went wrong while sending friend request',
      });
    }
  }, [referrerUser, sendFriendRequest, isSendingFriendRequest]);

  const handleSubmitReferralCode = useCallback(async () => {
    if (submitLoading) {
      return;
    }

    if (!referralCode || referralCode.trim() === '') {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: "this doesn't seem right\ncheck again".toUpperCase(),
      });
      return;
    }

    try {
      const success = await submitReferralCode(referralCode);
      if (success) {
        refreshCurrentUser();
        Analytics.track(EVENTS.PROFILE.REFERRAL_CODE_REDEMPTION_SUCCESS, {
          referralCode,
        });
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: 'Referral code redeemed successfully',
        });
        router.replace('/home');
      } else {
        setReferralCodeSubmissionError(true);
        showToast({
          type: TOAST_TYPE.ERROR,
          description: 'Invalid referral code',
        });
      }
    } catch (error) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: 'Something went wrong',
      });
    }
  }, [
    referralCode,
    submitReferralCode,
    submitLoading,
    router,
    refreshCurrentUser,
  ]);

  const handleResetError = useCallback(() => {
    setReferralCode('');
    setReferralCodeSubmissionError(false);
  }, []);

  // Computed values
  const shouldShowReferrerCard =
    showReferralInput &&
    _size(referralCode) >= MIN_LENGTH_OF_USERNAME &&
    referrerUser;

  const isReferralCodeEmpty = !referralCode || referralCode.length <= 0;

  const isWrongReferralCode =
    _size(referralCode) >= MIN_LENGTH_OF_USERNAME &&
    !fetchingUser &&
    _isEmpty(referrerUser);
  const isValidReferralCode =
    _size(referralCode) >= MIN_LENGTH_OF_USERNAME &&
    !fetchingUser &&
    !_isEmpty(referrerUser);

  return {
    // State
    referralCode,
    showReferralInput,
    referralCodeSubmissionError,
    sendFriendRequestSuccess,
    referrerUser:
      _size(referralCode) >= MIN_LENGTH_OF_USERNAME
        ? referrerUser
        : EMPTY_OBJECT,

    // Loading states
    submitLoading,
    fetchingUser,
    isSendingFriendRequest,

    // Computed values
    shouldShowReferrerCard,
    isReferralCodeEmpty,
    isWrongReferralCode,
    isValidReferralCode,

    // Actions
    handleSkipReferral,
    handleShowReferralInput,
    handleReferralCodeChange,
    handleSendFriendRequest,
    handleSubmitReferralCode,
    handleResetError,
  };
};

export default useReferralCodeState;
