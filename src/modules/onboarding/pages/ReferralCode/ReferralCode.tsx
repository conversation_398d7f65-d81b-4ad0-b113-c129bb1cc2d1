import React from 'react';
import { View } from 'react-native';

import BackButton from '@/src/components/molecules/BackButton';
import ProgressBar from '@/src/components/atoms/ProgressBar/ProgressBar';
import { useReferralCodePageStyles } from './ReferralCode.style';
import useReferralCodeState from './hooks/useReferralCodeState';
import useKeyboardHandler from '@/src/core/hooks/useKeyboardHandler';
import ReferralCodeView from './components/ReferralCodeView';

const ReferralCode: React.FC = () => {
  const styles = useReferralCodePageStyles();
  const referralState = useReferralCodeState();
  const keyboardState = useKeyboardHandler();

  return (
    <View style={styles.mainContainer}>
      <View style={styles.header}>
        <BackButton />
        <ProgressBar currentStep={3} totalSteps={3} />
      </View>

      <ReferralCodeView 
        styles={styles}
        referralState={referralState}
        keyboardState={keyboardState}
      />
    </View>
  );
};

export default ReferralCode;
