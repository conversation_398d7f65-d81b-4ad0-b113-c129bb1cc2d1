import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import font from '@/src/core/constants/fonts';
import fonts from '@/src/theme/fonts';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: dark.colors.background,
    },
    contentContainer: {
      marginTop: '30%',
    },
    animatedContainer: {
      flex: 1,
      justifyContent: 'space-between',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingRight: 20,
      paddingTop: isCompactMode ? 20 : 40,
      paddingBottom: 20,
      zIndex: 1,
      gap: 12,
      paddingLeft: 20,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: dark.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 20,
    },
    progressContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    progressStep: {
      flex: 1,
      height: 4,
      borderRadius: 2,
    },
    progressStepActive: {
      backgroundColor: dark.colors.secondary,
    },
    progressStepInactive: {
      backgroundColor: dark.colors.tertiary,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
    },
    title: {
      fontSize: 12,
      fontFamily: fonts.Montserrat_700,
      color: dark.colors.whiteLight,
      textAlign: 'center',
      letterSpacing: 1.5,
    },
    inputContainer: {
      width: '100%',
      alignItems: 'center',
      paddingHorizontal: 0,
      position: 'relative',
    },
    image: { position: 'absolute' },
    textInput: {
      width: '80%',
      height: 64,
      backgroundColor: dark.colors.background,
      fontSize: 12,
      fontFamily: fonts.Montserrat_700,
      color: dark.colors.whiteLight,
      textAlign: 'center',
      letterSpacing: 2,
      paddingHorizontal: 10,
    },
    textInputError: {
      borderBottomColor: dark.colors.errorDark,
    },
    errorText: {
      fontSize: 10,
      marginTop: 20,
      textAlign: 'center',
      fontFamily: font.MONTSERRAT_BOLD,
    },
    checkingText: {
      fontSize: 12,
      fontFamily: 'Montserrat-500',
      color: dark.colors.tertiary,
      textAlign: 'center',
      marginTop: 8,
    },
    successText: {
      fontSize: 12,
      fontFamily: 'Montserrat-500',
      color: dark.colors.secondary,
      textAlign: 'center',
      marginTop: 8,
    },
    footer: {
      paddingHorizontal: 20,
      paddingBottom: isCompactMode ? 20 : 40,
      paddingTop: 20,
      backgroundColor: dark.colors.background,
    },
    newUserGetStartedButton: {
      width: '100%',
      minWidth: 200,
      justifyContent: 'center',
      alignItems: 'center',
      height: 51,
      borderRadius: 12,
    },
    newUserGetStartedButtonStyle: {
      borderWidth: 0.8,
      borderColor: dark.colors.secondary,
      borderRadius: 10,
      height: 51,
    },
    newUserGetStartedLabel: {
      fontSize: 12,
      letterSpacing: 2,
      fontFamily: fonts.Montserrat_700,
      color: 'white',
    },
    newUserGetStartedButtonBackground: {
      backgroundColor: dark.colors.victoryColor,
    },
    textInputBottomBorder: {
      width: '80%',
      height: 1,
      position: 'absolute',
      bottom: 0,
    },
  });

export const useUsernameStyles = () => {
  const { isMobile } = useMediaQuery();
  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};
