import React from 'react';
import { View } from 'react-native';
import BackButton from '@/src/components/molecules/BackButton';
import ProgressBar from '@/src/components/atoms/ProgressBar/ProgressBar';
import { useUsernameStyles } from '../Username.style';

interface UsernameHeaderProps {
  currentStep: number;
  totalSteps: number;
}

export const UsernameHeader: React.FC<UsernameHeaderProps> = ({
  currentStep,
  totalSteps,
}) => {
  const styles = useUsernameStyles();

  return (
    <View style={styles.header}>
      <BackButton />
      <ProgressBar currentStep={currentStep} totalSteps={totalSteps} />
    </View>
  );
};
