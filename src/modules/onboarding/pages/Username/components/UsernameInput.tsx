import React from 'react';
import { TextInput, View } from 'react-native';
import LinearGradient from 'atoms/LinearGradient';
import dark from '@/src/core/constants/themes/dark';
import { useUsernameStyles } from '../Username.style';

interface UsernameInputProps {
  username: string;
  onChangeText: (text: string) => void;
  hasError: boolean;
}

export const UsernameInput: React.FC<UsernameInputProps> = ({
  username,
  onChangeText,
  hasError,
}) => {
  const styles = useUsernameStyles();

  const gradientColors = hasError
    ? ['#D24A4A', '#D24A4A']
    : ['#A9F99E', '#00D9FF'];

  return (
    <View style={styles.inputContainer}>
      <TextInput
        style={[styles.textInput]}
        value={username}
        onChangeText={onChangeText}
        placeholderTextColor={dark.colors.inputPlaceholder}
        maxLength={20}
        textAlign="center"
      />
      <LinearGradient
        style={styles.textInputBottomBorder}
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      />
    </View>
  );
};
