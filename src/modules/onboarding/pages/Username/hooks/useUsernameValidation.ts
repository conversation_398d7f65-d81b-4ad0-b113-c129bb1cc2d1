import { useEffect, useState } from 'react';
import useCheckIsUserNameAvailable from '@/src/modules/profile/hooks/query/useCheckIsUserNameAvailable';

interface UseUsernameValidationProps {
  username: string;
}

interface UsernameValidationState {
  isAvailable: boolean;
  loading: boolean;
  error: any;
  hasChecked: boolean;
  isValidLength: boolean;
}

export const useUsernameValidation = ({
  username,
}: UseUsernameValidationProps): UsernameValidationState => {
  const { isAvailable, loading, error, checkUserNameAvailability } =
    useCheckIsUserNameAvailable();
  const [hasChecked, setHasChecked] = useState(false);
  const [checkTimeout, setCheckTimeout] = useState<NodeJS.Timeout | null>(null);

  const trimmedUsername = username.trim();
  const isValidLength = trimmedUsername.length >= 3;

  // Check username availability with debouncing
  useEffect(() => {
    if (checkTimeout) {
      clearTimeout(checkTimeout);
    }

    if (isValidLength) {
      const timeout = setTimeout(() => {
        checkUserNameAvailability({ username: trimmedUsername });
        setHasChecked(true);
      }, 500); // 500ms debounce

      setCheckTimeout(timeout);
    } else {
      setHasChecked(false);
    }

    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
    };
  }, [trimmedUsername, checkUserNameAvailability, isValidLength]);

  return {
    isAvailable,
    loading,
    error,
    hasChecked,
    isValidLength,
  };
};
