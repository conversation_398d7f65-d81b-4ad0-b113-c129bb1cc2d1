import { useCallback } from 'react';
import { Keyboard } from 'react-native';

interface UseUsernameActionsProps {
  username: string;
  canContinue: boolean;
  onContinue?: (username: string) => void;
}

interface UsernameActions {
  handleContinue: () => void;
  getButtonLabel: (cantProceed: boolean, canContinue: boolean) => string;
}

const getButtonLabel = (cantProceed: boolean, canContinue: boolean): string => {
  if (cantProceed) {
    return "CAN'T PROCEED";
  }
  return 'CONTINUE';
};

export const useUsernameActions = ({ 
  username, 
  canContinue, 
  onContinue 
}: UseUsernameActionsProps): UsernameActions => {
  
  const handleContinue = useCallback(() => {
    if (canContinue && onContinue) {
      Keyboard.dismiss();
      onContinue(username.trim());
    }
  }, [username, onContinue, canContinue]);

  return {
    handleContinue,
    getButtonLabel,
  };
};
