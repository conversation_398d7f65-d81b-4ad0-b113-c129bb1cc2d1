import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import fonts from '@/src/theme/fonts';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 20,
    paddingTop: 20,
    paddingBottom: 20,
    zIndex: 1,
    gap: 12,
  },
  buttonContainer: {
    marginTop: 80,
    paddingHorizontal: 16,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    gap: 25,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    paddingHorizontal: 16,
  },
  continueButton: {
    borderWidth: 0.8,
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    height: 60,
  },
  continueButtonContainer: {
    flex: 1,
    width: '100%',
    minWidth: 200,
    justifyContent: 'center',
    alignItems: 'center',
    maxWidth: 400,
    height: 60,
    borderRadius: 12,
  },
  continueButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    // lineHeight: 17,
    color: dark.colors.textLight,
    letterSpacing: 2,
  },
  continueBackground: {
    flex: 1,
    backgroundColor: dark.colors.victoryColor,
  },
  skipButton: {
    height: 60,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noReferralCode: {
    fontFamily: fonts.Montserrat_700,
    fontSize: 12,
    color: dark.colors.textLight,
    opacity: 0.4,
    letterSpacing: 1,
  },
});

export default styles;
