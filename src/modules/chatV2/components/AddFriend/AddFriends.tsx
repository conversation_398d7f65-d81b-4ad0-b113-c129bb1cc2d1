import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Dark from '@/src/core/constants/themes/dark';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from '@/src/core/constants/themes/dark';
import { useRouter } from 'expo-router';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import handShake from '@/assets/images/chat/hand-shake.png';
import Header from '@/src/components/shared/Header';

const AddFriends = () => {
  const router = useRouter();
  const { user }: any = useSession();
  const onPress = () => {
    router.push(`/profile/${user?.username}/friends`);
  };

  return (
    <View style={styles.mainContainer}>
      <Header title="Messages" />
      <View style={styles.container}>
        <View style={styles.addFriendsContainer}>
          <Image source={handShake} style={styles.image} />
          <Text style={styles.addFriendsText}>
            Your inbox is waiting. Add friends and make it count - one duel at a
            time.
          </Text>
        </View>

        <InteractiveSecondaryButton
          onPress={onPress}
          borderComponentStyle={styles.borderComponentStyle}
          borderColor={dark.colors.secondary}
          label="Add Friends"
          labelStyle={[styles.labelStyle]}
          buttonStyle={[styles.buttonStyle]}
          buttonContainerStyle={[styles.buttonContainerStyle]}
          buttonContentStyle={styles.buttonContentStyle}
        />
      </View>
    </View>
  );
};

export default AddFriends;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Dark.colors.background,
  },
  container: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingBottom: '2%',
  },
  image: {
    width: 100,
    height: 100,
  },
  addFriendsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 20,
    width: 240,
  },
  addFriendsText: {
    textAlign: 'center',
    fontSize: 11,
    fontFamily: 'Montserrat-500',
    lineHeight: 16,
    color: dark.colors.textLight,
  },
  borderComponentStyle: {
    position: 'absolute',
    bottom: 0.5,
  },
  labelStyle: {
    fontSize: 10,
    fontFamily: 'Montserrat-700',
    lineHeight: 12,
    color: dark.colors.textLight,
  },
  buttonStyle: {
    height: 38,
  },
  buttonContainerStyle: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    height: 43,
    width: 148,
    marginTop: 18,
  },
  buttonContentStyle: {
    gap: 4,
  },
});
