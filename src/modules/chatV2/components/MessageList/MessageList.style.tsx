import Dark from '@/src/core/constants/themes/dark';
import { Platform, StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Dark.colors.background,
  },
  header: {
    height: 56,
    backgroundColor: Dark.colors.background,
    paddingLeft: 4,
    gap: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    color: '#fff',
    fontSize: 24,
  },
  headerProfile: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerAvatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 28,
    height: 28,
    borderRadius: 8,
    overflow: 'hidden',
  },
  headerAvatar: {
    width: 28,
    height: 28,
  },
  headerInfo: {},
  headerName: {
    color: Dark.colors.textLight,
    fontSize: 17,
    lineHeight: 24,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  messagesList: {
    padding: 16,
    flex: 1,
  },
  messageWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%',
    flex: 1,
  },
  messageContainer: {
    maxWidth: '75%',
  },
  sentMessage: {
    justifyContent: 'flex-end',
  },
  receivedMessage: {
    justifyContent: 'flex-start',
  },
  messageBubble: {
    borderRadius: 12,
    paddingHorizontal: 13,
    paddingVertical: 8,
    backgroundColor: Dark.colors.gradientBackground,
    minWidth: 120,
  },
  messageTime: {
    fontFamily: 'Montserrat-400',
    color: Dark.colors.textDark,
    fontSize: 10,
    alignSelf: 'flex-end',
  },
  footer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    paddingTop: 16,
    // marginTop: 24,
    flexDirection: 'row',
    backgroundColor: 'transparent',
    alignItems: 'center',
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: Dark.colors.primary,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
    position: 'relative',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    color: '#fff',
    backgroundColor: 'transparent',
    fontSize: 16,
    maxHeight: 120,
    paddingRight: 50,
    // maxHeight: 100,
    fontFamily: 'Montserrat-400',
    marginRight: 8,
    lineHeight: 20,
    ...Platform.select({
      web: {
        outlineWidth: 0,
        // height: 24,
        // height: 'fit-content',
        lineHeight: 24,
        overflow: 'hidden',
      },
    }),
  },
  attachButton: {
    padding: 4,
  },
  attachButtonText: {
    color: Dark.colors.secondary,
    fontSize: 24,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'absolute',
    paddingRight: 11,
    right: 0,
  },
  sendButtonImage: {
    width: 24,
    height: 24,
  },
  sendButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: Dark.colors.secondary,
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 20,
  },
  linkText: {
    color: Dark.colors.linkColor,
    textDecorationLine: 'underline',
    paddingBottom: 4,
  },

  // Update messageText to handle inline links better
  messageText: {
    fontSize: 13,
    marginBottom: 4,
    fontFamily: 'Montserrat-400',
    color: '#fff',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});
export default styles;
