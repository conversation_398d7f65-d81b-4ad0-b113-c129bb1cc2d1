import React from 'react';
import { Linking, Text } from 'react-native';
import _split from 'lodash/split';
import _trim from 'lodash/trim';
import { useRouter } from 'expo-router';
import _some from 'lodash/some';
import _startsWith from 'lodash/startsWith';
import styles from './MessageListItem.style';

const VALID_MATIKS_APP_URL_PREFIXES = [
  'https://www.matiks.com',
  'https://matiks.com',
  'https://www.matiks.in',
  'https://matiks.in',
  'https://www.matiks.org',
  'https://matiks.org',
];

// URL regex pattern
const URL_REGEX = /(https?:\/\/[^\s]+)/g;

const MessageUrl = ({ url }: { url: string }) => {
  const router = useRouter();
  const isMatiksUrl = _some(VALID_MATIKS_APP_URL_PREFIXES, (prefix) =>
    _startsWith(url, prefix),
  );

  const handlePress = () => {
    if (isMatiksUrl) {
      try {
        const urlObject = new URL(url);
        const path = urlObject.pathname;
        router.push(path);
      } catch (error) {
        // Fallback to regular URL opening if URL parsing fails
        Linking.openURL(url);
      }
    } else {
      Linking.openURL(url);
    }
  };

  return (
    <Text key={url} style={styles.linkText} onPress={handlePress}>
      {url}
    </Text>
  );
};

const MessageContent = ({ content }: { content: string }) => {
  if (!content) return null;

  const parts = _split(content, URL_REGEX);

  return parts.map((part, index) => {
    if (part.match(URL_REGEX)) {
      return <MessageUrl url={_trim(part)} />;
    }
    return (
      <Text key={index} style={styles.messageText}>
        {_trim(part)}
      </Text>
    );
  });
};

export default React.memo(MessageContent);
