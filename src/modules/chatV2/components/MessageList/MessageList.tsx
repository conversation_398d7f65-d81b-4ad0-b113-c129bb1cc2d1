import React, { useCallback, useEffect, useRef } from 'react';
import {
  FlatList,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { FlashList } from '@shopify/flash-list';
import useChatDetailBackPressHandler from 'modules/chatV2/hooks/useChatDetailBackPressHandler';
import useChatStore from '@/src/store/useChatStore';
import { useRouter } from 'expo-router';
import _get from 'lodash/get';
import _size from 'lodash/size';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { Message } from '../../types/messages';
import Header from './MessageListHeader';
import styles from './MessageList.style';
import MessageItem from './MessageListItem';
import Footer from './MessageListFooter';
import ListFooter from '../ListFooter';
import NoChatsPage from './NoChatsPage';

const List = Platform.select({
  native: FlashList as any,
  web: FlatList as any,
});

const MessageList = ({
  showHeader = true,
  isGroupChat = false,
  currentGroupId,
}: {
  showHeader?: boolean;
  isGroupChat?: boolean;
  currentGroupId: string;
}) => {
  const { userId }: any = useSession();

  const router = useRouter();

  const {
    messagesLoading,
    messages,
    currentGroup,
    updateLastMessageRead,
    fetchMessages,
  } = useChatStore((state) => ({
    messagesLoading: state.isMessagesLoading[currentGroupId],
    messages: state.messages[currentGroupId],
    currentGroup: state.messageGroups[currentGroupId],
    updateLastMessageRead: state.updateLastMessageReadStateWebSocket,
    fetchMessages: state.fetchMessages,
  }));

  const topMessageId = _get(messages, [0, '_id'], null);
  const isTopMessageMine = _get(messages, [0, 'sender'], null) === userId;
  const onBackPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.MESSAGES.CLICK_ON_BACK);
    router.replace('/chat');
    return true;
  }, [router]);

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.MESSAGES.VIEWED_MESSAGES_PAGE);
  }, []);

  useChatDetailBackPressHandler(onBackPress);
  const renderMessage = useCallback(
    ({ item }: { item: Message }) => (
      <MessageItem item={item} userId={userId} showSenderInfo={isGroupChat} />
    ),
    [userId, isGroupChat],
  );

  const ListFooterComponent = useCallback(
    () => <ListFooter loading={messagesLoading} />,
    [messagesLoading],
  );

  const fetchMessagesRef = useRef(fetchMessages);
  fetchMessagesRef.current = fetchMessages;
  const updateLastMessageReadRef = useRef(updateLastMessageRead);
  updateLastMessageReadRef.current = updateLastMessageRead;

  useEffect(() => {
    if (!currentGroupId || !topMessageId || isTopMessageMine) return;
    (async () => {
      await fetchMessagesRef.current(currentGroupId);
      await updateLastMessageReadRef.current(currentGroupId, topMessageId);
    })();
  }, [currentGroupId, topMessageId, isTopMessageMine]);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[styles.container]}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 24}
      >
        {showHeader && (
          <Header group={currentGroup} onBackPress={onBackPress} />
        )}
        {_size(messages) === 0 ? <NoChatsPage /> : null}
        <List
          data={messages as any[]}
          renderItem={renderMessage}
          keyExtractor={(item: any, index: any) => `${item._id}__${index}`}
          contentContainerStyle={styles.messagesList}
          onEndReachedThreshold={0.5}
          onEndReached={() => {
            fetchMessages(currentGroupId);
          }}
          ListFooterComponent={ListFooterComponent as any}
          inverted
          showsVerticalScrollIndicator={false}
        />
        <Footer currentGroupId={currentGroupId} />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default React.memo(MessageList);
