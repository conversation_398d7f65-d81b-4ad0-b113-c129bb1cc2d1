import React from 'react';
import useChatStore from '@/src/store/useChatStore';
import Loading from '@/src/components/atoms/Loading';
import _size from 'lodash/size';
import MessageList from './MessageList';

const MessageListWrapper = ({ currentGroupId }: { currentGroupId: string }) => {
  const { messages, isMessagesLoading } = useChatStore(
    (state) => ({
      messages: state.messages[currentGroupId],
      isMessagesLoading: state.isMessagesLoading[currentGroupId],
    }),
  );

  if (isMessagesLoading && _size(messages) === 0) {
    return <Loading />;
  }

  return <MessageList currentGroupId={currentGroupId} />;
};

export default React.memo(MessageListWrapper);
