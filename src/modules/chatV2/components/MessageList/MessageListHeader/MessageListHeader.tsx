import React, { useCallback } from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import { Icon } from '@rneui/themed';
import Dark from 'core/constants/themes/dark';
import { Group } from 'modules/chatV2/types/groups';
import groupReader from 'modules/chatV2/readers/groupReader';
import styles from './MessageListHeader.style';
import { useRouter } from 'expo-router';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

const MessageListHeader = ({
  group,
  onBackPress,
}: {
  group: Group;
  onBackPress: () => void;
}) => {
  const user = groupReader.individual(group);
  const avatar = user?.profileImageUrl;
  const name = user?.name;

  const router = useRouter();

  const onPressAvatar = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.MESSAGES.CLICK_ON_PROFILE);
    router.push(`/profile/${user?.username}`);
  }, [user?.username, router]);

  return (
    <View style={styles.container}>
      <Pressable style={styles.backButton} onPress={onBackPress}>
        <Icon
          name="chevron-left"
          type="font-awesome-5"
          color={Dark.colors.textLight}
          size={16}
        />
      </Pressable>

      <View style={styles.headerProfile}>
        <Pressable onPress={onPressAvatar} style={styles.headerAvatarContainer}>
          <Image source={{ uri: avatar }} style={styles.headerAvatar} />
        </Pressable>
        <Pressable onPress={onPressAvatar} style={styles.headerInfo}>
          <Text style={styles.headerName}>{name}</Text>
        </Pressable>
      </View>
    </View>
  );
};

export default React.memo(MessageListHeader);
