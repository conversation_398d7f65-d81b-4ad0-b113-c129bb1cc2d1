import Dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    height: 56,
    backgroundColor: Dark.colors.background,
    paddingLeft: 4,
    gap: 4,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: Dark.colors.tertiary,
    borderBottomWidth: 0.01,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerProfile: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerAvatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 28,
    height: 28,
    borderRadius: 8,
    overflow: 'hidden',
  },
  headerAvatar: {
    width: 28,
    height: 28,
  },
  headerInfo: {},
  headerName: {
    color: Dark.colors.textLight,
    fontSize: 17,
    lineHeight: 24,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
});
export default styles;
