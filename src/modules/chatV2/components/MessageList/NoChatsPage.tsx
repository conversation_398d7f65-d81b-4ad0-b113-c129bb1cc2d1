import { View, Text, Image } from 'react-native';
import React from 'react';
import noFriendsPookie from '@/assets/images/chat/hi-hand-gesture.png';
import Dark from '@/src/core/constants/themes/dark';

const NoChatsPage = () => {
  return (
    <View
      style={{
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        gap: 20,
      }}
    >
      <Image source={noFriendsPookie} style={{ width: 100, height: 100 }} />
      <Text
        style={{
          fontSize: 11,
          lineHeight: 16,
          fontFamily: 'Montserrat-600',
          color: Dark.colors.textLight,
        }}
      >
        Hi first. Duel next. Mathmates forever.
      </Text>
    </View>
  );
};

export default NoChatsPage;
