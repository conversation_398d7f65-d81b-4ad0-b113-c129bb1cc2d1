/* eslint-disable no-nested-ternary */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { SafeAreaView } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import _size from 'lodash/size';
import Loading from '@/src/components/atoms/Loading';
import useChatStore from '@/src/store/useChatStore';
import { useRouter } from 'expo-router';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { Group } from '../../../types/groups';
import PeopleListHeader from '../PeopleListHeader';
import PeopleListCard from '../PeopleListCard';
import styles from './CompactPeopleList.style';
import ListFooter from '../../ListFooter';
import EmptyComponent from '../../EmptyComponent';

const CompactPeopleList = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const {
    isMessageGroupsLoading,
    messageGroupsShown,
    searchGroups,
    fetchMessageGroups,
  } = useChatStore((state) => ({
    isMessageGroupsLoading: state.isMessageGroupsLoading,
    messageGroupsShown: state.messageGroupsShown,
    searchGroups: state.searchGroups,
    fetchMessageGroups: state.fetchMessageGroups,
  }));

  const searchGroupsRef = useRef(searchGroups);
  searchGroupsRef.current = searchGroups;

  const fetchMessageGroupsRef = useRef(fetchMessageGroups);
  fetchMessageGroupsRef.current = fetchMessageGroups;

  useEffect(() => {
    // fetch for new message groups
    fetchMessageGroupsRef.current?.();
  }, []);

  useEffect(() => {
    searchGroupsRef.current?.(searchQuery);
  }, [searchQuery]);

  const renderListItem = useCallback(
    ({ item }: { item: Group }) => (
      <PeopleListCard
        item={item}
        onPress={() => {
          Analytics.track(ANALYTICS_EVENTS.MESSAGES.CLICKED_ON_INDIVIDUAL_CHAT);
          router.push(`/chat?groupId=${item?._id}`);
          setSearchQuery('');
        }}
      />
    ),
    [router],
  );

  const ListFooterComponent = useCallback(
    () => <ListFooter loading={isMessageGroupsLoading} />,
    [isMessageGroupsLoading],
  );

  return (
    <SafeAreaView style={styles.container}>
      <PeopleListHeader search={searchQuery} setSearch={setSearchQuery} />
      {_size(messageGroupsShown) === 0 ? (
        isMessageGroupsLoading ? (
          <Loading />
        ) : (
          <EmptyComponent />
        )
      ) : (
        <FlashList
          data={messageGroupsShown}
          renderItem={renderListItem}
          keyExtractor={(item, index) => `${item._id}__${index}`}
          contentContainerStyle={styles.listContainer}
          // onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={ListFooterComponent as any}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

export default React.memo(CompactPeopleList);
