import Dark from 'core/constants/themes/dark';
import { Platform, StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    width: 352,
    height: '100%',
    paddingHorizontal: 16,
    paddingVertical: 20,
    gap: 8,
    borderWidth: 1,
    borderColor: Dark.colors.tertiary,
    borderRadius: 12,
  },
  searchContainer: {
    width: '100%',
    height: 36,
    borderColor: Dark.colors.tertiary,
    borderWidth: 2,
    backgroundColor: Dark.colors.gradientBackground,
    borderRadius: 8,
    flexDirection: 'row',
    paddingRight: 2,
    paddingLeft: 12,
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    backgroundColor: 'transparent',
    height: 20,
    lineHeight: 20,
    fontSize: 13,
    fontFamily: 'Montserrat-400',
    color: Dark.colors.textLight,
    ...Platform.select({
      web: {
        outline: 'none',
      },
    }),
  },
  searchIconContainer: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default styles;
