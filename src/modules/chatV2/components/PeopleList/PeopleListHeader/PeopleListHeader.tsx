import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Animated,
  TextInput,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import { Icon } from '@rneui/themed';
import Ionicons from '@expo/vector-icons/Ionicons';
import styles from './PeopleListHeader.style';
import { router } from 'expo-router';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

const Header = ({
  search,
  setSearch,
}: {
  search: string;
  setSearch: (value: string) => void;
}) => {
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const searchWidth = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const toggleSearch = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.MESSAGES.CLICKED_ON_SEARCH_BAR);
    const toValue = isSearchVisible ? 0 : 1;
    setIsSearchVisible(!isSearchVisible);

    Animated.parallel([
      Animated.timing(searchWidth, {
        toValue,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(opacity, {
        toValue,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  }, [isSearchVisible, searchWidth, opacity]);

  const handleBackButtonPress = useCallback(() => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace('/home');
    }
  }, [router]);

  return (
    <View style={styles.headerContainer}>
      <View style={[styles.titleContainer, { flex: 1 }]}>
        <Pressable onPress={handleBackButtonPress}>
          <View style={styles.backButton}>
            <Icon
              name="chevron-left"
              type="font-awesome-5"
              color={Dark.colors.textLight}
              size={16}
            />
          </View>
        </Pressable>
        <Animated.Text
          style={[
            styles.headerTitle,
            {
              opacity: opacity.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 0],
              }),
            },
          ]}
        >
          Messages
        </Animated.Text>
        <Animated.View
          style={[
            styles.searchContainer,
            {
              opacity,
              transform: [
                {
                  scaleX: searchWidth.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                },
              ],
              transformOrigin: 'left',
            },
          ]}
        >
          {isSearchVisible && (
            <TextInput
              style={styles.searchInput}
              value={search}
              onChangeText={setSearch}
              placeholder="Search ...."
              placeholderTextColor="#777"
              autoFocus
            />
          )}
        </Animated.View>
      </View>
      <TouchableOpacity
        onPress={toggleSearch}
        style={{
          height: 40,
          width: 40,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Ionicons
          name={isSearchVisible ? 'close' : 'search-sharp'}
          style={{ transform: [{ rotate: '5deg' }] }}
          size={18}
          color={Dark.colors.textLight}
        />
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(Header);
