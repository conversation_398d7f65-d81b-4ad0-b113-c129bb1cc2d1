import Dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Dark.colors.background,
  },
  unreadIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Dark.colors.secondary,
  },
  chatItemContainer: {
    overflow: 'hidden',
    paddingVertical: 16,
    minHeight: 81,
    borderBottomColor: Dark.colors.tertiary,
    borderBottomWidth: 1,
    paddingHorizontal: 12,
  },
  activeChatItemContainer: {
    backgroundColor: Dark.colors.tertiary,
  },
  unreadChatItemContainer: {
    backgroundColor: Dark.colors.primary,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  readMessageText: {
    color: Dark.colors.textDark,
  },
  avatarContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    width: 44,
    height: 44,
  },
  avatar: {
    width: 44,
    height: 44,
  },
  onlineIndicator: {
    position: 'absolute',
    right: 0,
    bottom: -0.5,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#B9FACA',
    borderWidth: 2,
    borderColor: '#1E1E1E',
  },
  messageContent: {
    flex: 1,
    // height: 49,
    gap: 4,
    // backgroundColor: '#fff',
  },
  messageHeader: {
    flexDirection: 'row',

    justifyContent: 'space-between',
    // alignItems: 'center',
    // backgroundColor: '#fff',
    // marginBottom: 6,
  },
  userName: {
    fontSize: 14,
    textTransform: 'capitalize',
    fontFamily: 'Montserrat-600',
    color: Dark.colors.textLight,
    lineHeight: 17.07,
  },
  timeText: {
    fontSize: 11,
    color: '#BABABA',
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageText: {
    flex: 1,
    fontSize: 10,
    lineHeight: 14,
    color: Dark.colors.textLight,
    marginRight: 8,
    fontFamily: 'Montserrat-500',
  },
  unreadBadge: {
    backgroundColor: '#46ADD5',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  unreadCount: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default styles;
