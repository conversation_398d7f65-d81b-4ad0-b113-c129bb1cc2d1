import React, { useMemo } from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import _get from 'lodash/get';
import groupReader from '@/src/modules/chatV2/readers/groupReader';
import _isNaN from 'lodash/isNaN';
import _isArray from 'lodash/isArray';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useLocalSearchParams } from 'expo-router';
import styles from './PeopleListCard.style';

function timeSince(lastMessageTimeStr: string) {
  const lastMessageTime = new Date(lastMessageTimeStr);
  if (!lastMessageTime || _isNaN(lastMessageTime)) return '';
  const now = new Date();
  const diffInSeconds = Math.floor(
    (now.getTime() - lastMessageTime.getTime()) / 1000,
  );
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInMonths / 12);

  if (_isNaN(diffInSeconds)) {
    return '';
  }

  if (diffInYears > 0) {
    if (diffInYears === 1) {
      return '1 year ago';
    }
    return `${diffInYears} years ago`;
  }
  if (diffInMonths > 0) {
    if (diffInMonths === 1) {
      return '1 month ago';
    }
    return `${diffInMonths} mon ago`;
  }
  if (diffInDays > 0) {
    if (diffInDays === 1) {
      return '1 day ago';
    }
    return `${diffInDays} days ago`;
  }
  if (diffInHours > 0) {
    if (diffInHours === 1) {
      return '1 hour ago';
    }
    return `${diffInHours} hours ago`;
  }
  if (diffInMinutes > 0) {
    if (diffInMinutes === 1) {
      return '1 min ago';
    }
    return `${diffInMinutes} min(s) ago`;
  }
  if (diffInSeconds > 0) {
    if (diffInSeconds === 1) {
      return '1 second ago';
    }
    return `${diffInSeconds} sec(s) ago`;
  }
  return '';
}

const PeopleListCard = ({
  item,
  onPress,
  containerStyle = {},
}: {
  item: any;
  onPress: () => void;
  containerStyle?: any;
}) => {
  const { userId } = useSession();
  const params = useLocalSearchParams();
  const currentGroupId = useMemo(() => params?.groupId as string, [params]);

  const isMessageRead = useMemo(() => {
    if (_isArray(item?.lastMessageRead)) {
      const lastMessageReadInfo = item?.lastMessageRead.find(
        (user: any) => user.userId === userId,
      );
      return lastMessageReadInfo?.lastMessageRead === item?.lastMessage?._id;
    }
    return false;
  }, [item, userId]);
  const userInfo = groupReader.individual(item) ?? EMPTY_OBJECT;
  const profileImageUrl = _get(userInfo, 'profileImageUrl', '');
  const name = _get(userInfo, 'name', '');
  const lastMessage = _get(item, 'lastMessage', '');
  const isActive = useMemo(
    () => currentGroupId === _get(item, '_id', null),
    [currentGroupId, item],
  );

  return (
    <Pressable
      style={[
        styles.chatItemContainer,
        containerStyle,
        isActive && styles.activeChatItemContainer,
        !isMessageRead && styles.unreadChatItemContainer,
      ]}
      onPress={() => onPress?.()}
    >
      <View style={[styles.chatItem]}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: profileImageUrl }} style={styles.avatar} />
        </View>

        <View style={styles.messageContent}>
          <View style={styles.messageHeader}>
            <Text style={styles.userName}>{name}</Text>
            <Text style={styles.timeText}>
              {timeSince(lastMessage?.createdAt)}
            </Text>
          </View>

          <View style={styles.messageFooter}>
            <Text
              style={[
                styles.messageText,
                isMessageRead && styles.readMessageText,
              ]}
              numberOfLines={2}
            >
              {lastMessage?.content ?? `You and ${name} are now Friends...`}
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default React.memo(PeopleListCard);
