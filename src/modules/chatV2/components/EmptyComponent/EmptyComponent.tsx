import { Text, Image, TouchableOpacity, View } from 'react-native';
import React, { useCallback } from 'react';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from '@/src/core/constants/themes/dark';
import noFriendsPookie from '@/assets/images/pookie/no_friends_pookie.png';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useRouter } from 'expo-router';
import styles from './EmptyComponent.style';

const EmptyComponent = () => {
  const { user }: any = useSession();
  const router = useRouter();
  const handleAddFriends = useCallback(() => {
    router.push(`/profile/${user?.username}/friends`);
  }, [router, user]);

  return (
    <View style={styles.emptyStateContainer}>
      <Image source={noFriendsPookie} style={styles.pookieImage} />
      <Text style={styles.emptyStateText}>NO FRIENDS TO SHOW</Text>
      <Text style={styles.emptyStateSubText}>
        Check in the friends page to chat to friends not here or add new friends
      </Text>
      <TouchableOpacity
        style={styles.addFriendsButton}
        onPress={handleAddFriends}
      >
        <MaterialIcons name="add" size={16} color={dark.colors.secondary} />
        <Text style={styles.addFriendsButtonText}>Add friends</Text>
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(EmptyComponent);
