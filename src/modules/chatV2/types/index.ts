import { Group } from "./groups";
import { CreateMessageInput, Message } from "./messages";

export enum ChatAttachmentType {
  IMAGE = 'IMAGE',
  FILE = 'FILE',
}

export interface Attachment {
  _id: string;
  type: ChatAttachmentType;
  url: string;
}
export interface ChatDetails {
  _id: string;
  channel: string;
  members: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatGroup {
  _id: string;
  channel: string;
}

export type LAST_MESSAGE_ID = string | null;

export type MessagesParams = {
  lastMessageId: LAST_MESSAGE_ID;
  hasMore: boolean;
};

export type GroupsParams = {
  nextPage: number;
  hasMore: boolean;
};

export type MessagingContextType = {
  messages: Message[];
  messagesLoading: boolean;
  messagesError: any;
  loadMoreMessages: () => void;
  hasMoreMessages: boolean;
  sendNewMessage: (message: CreateMessageInput) => void;

  // Groups related
  groups: Group[];
  groupsLoading: boolean;
  groupsError: any;
  loadMoreGroups: () => void;
  hasMoreGroups: boolean;

  // Current selected group
  currentGroupId: string | null;
  setCurrentGroupId: (groupId: string) => void;
  currentGroup: Group | {};
};
