import React from 'react';
import useChatStore from 'store/useChatStore';
import _keys from 'lodash/keys';
import AddFriends from '../../components/AddFriend';
import ChatDetails from './ChatDetails';
import Loading from '@/src/components/atoms/Loading';

interface Props {
  groupId: string;
}

interface State {
  messageGroups: Record<string, any>;
  isMessageGroupsLoading: boolean;
}

const ChatDetailsWrapper = ({ groupId }: Props) => {
  const { messageGroups, isMessageGroupsLoading }: State = useChatStore((state) => ({
    messageGroups: state.messageGroups,
    isMessageGroupsLoading: state.isMessageGroupsLoading,
  }));

  if (isMessageGroupsLoading && _keys(messageGroups).length === 0) {
    return <Loading />;
  }

  if (_keys(messageGroups).length === 0) {
    return <AddFriends />;
  }

  return (
    <ChatDetails groupId={groupId} />
  );
};

export default ChatDetailsWrapper;
