import { View } from 'react-native';
import React, { useCallback, useState } from 'react';
import PeopleList from '@/src/modules/chatV2/components/PeopleList/CompactPeopleList';
import MessageList from '@/src/modules/chatV2/components/MessageList/MessageList';
import useEscKey from 'core/hooks/useEscKey';
import { useRouter } from 'expo-router';

const CompactChatDetails = ({ groupId }: { groupId: string | null }) => {
  const [currentGroupId, setCurrentGroupId] = useState<string | null>(groupId);
  const router = useRouter();

  const onEscPress = useCallback(() => {
    setCurrentGroupId(null);
    router.replace('/chat');
  }, [router]);

  useEscKey(onEscPress);

  return (
    <View style={{ flex: 1 }}>
      {currentGroupId === null ? (
        <PeopleList />
      ) : (
        <MessageList currentGroupId={groupId ?? ''} />
      )}
    </View>
  );
};

export default React.memo(CompactChatDetails);
