import Dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

export const expandedStyles = StyleSheet.create({
  expandedWrapper: {
    paddingHorizontal: 48,
    paddingTop: 12,
    paddingBottom: 24,
    minHeight: 600,
    width: '100%',
    height: '100%',
  },
  expandedContainer: {
    height: '100%',
    width: '100%',
    flexDirection: 'row',
    borderRadius: 12,
    gap: 16,
    borderColor: Dark.colors.tertiary,
    borderWidth: 0.5,
    overflow: 'hidden',
    padding: 6,
  },
  messageListContainer: {
    flex: 1,
    height: '100%',
    width: '100%',
    gap: 16,
    overflow: 'hidden',
    borderColor: Dark.colors.tertiary,
    borderWidth: 1,
    borderRadius: 12,
  },
  emptyStateContainer: {
    flex: 1,
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Dark.colors.background,
    borderRadius: 12,
    borderColor: Dark.colors.tertiary,
    borderWidth: 1,
    padding: 20,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyImage: {
    width: 160,
    height: 108,
    opacity: 0.95,
    transform: [{ scale: 1.1 }],
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    color: Dark.colors.text,
    marginBottom: 12,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(169, 249, 158, 0.15)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 10,
  },
  subtitleContainer: {
    backgroundColor: 'rgba(169, 249, 158, 0.05)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 32,
    maxWidth: '70%',
  },
  emptySubtitle: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: Dark.colors.textDark,
    textAlign: 'center',
    lineHeight: 20,
  },
  divider: {
    width: '40%',
    height: 1,
    backgroundColor: Dark.colors.tertiary,
    marginBottom: 24,
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(169, 249, 158, 0.08)',
    padding: 14,
    borderRadius: 10,
    maxWidth: '80%',
    gap: 10,
    borderWidth: 1,
    borderColor: 'rgba(169, 249, 158, 0.15)',
    marginBottom: 24,
  },
  tipText: {
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    color: Dark.colors.textDarkHighLighted,
    flexShrink: 1,
    lineHeight: 18,
  },
  actionHint: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 16,
    opacity: 0.7,
  },
  actionHintText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: Dark.colors.textDark,
  },
});
