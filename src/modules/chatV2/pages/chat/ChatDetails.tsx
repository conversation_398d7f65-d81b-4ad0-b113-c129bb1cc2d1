import React from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import ExpandedChatDetails from './ExpandedChatDetails';
import CompactChatDetails from './CompactChatDetails';

const ChatDetails = ({ groupId }: { groupId: string }) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  return isCompactMode ? (
    <CompactChatDetails groupId={groupId} />
  ) : (
    <ExpandedChatDetails groupId={groupId} />
  );
};

export default React.memo(ChatDetails);
