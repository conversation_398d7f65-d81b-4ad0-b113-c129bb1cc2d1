import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dividerBar: {
    borderBottomColor: dark.colors.tertiary,
    borderBottomWidth: 1,
  },
  userImage: {
    height: 36,
    width: 36,
    borderRadius: 4,
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  userName: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    lineHeight: 17,
    maxWidth: 200,
    color: 'white',
  },
  userRating: {
    maxWidth: 100,
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    lineHeight: 15,
    color: dark.colors.textDark,
  },
  acceptText: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    lineHeight: 20,
    color: dark.colors.secondary,
  },
  rejectText: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    lineHeight: 20,
    color: dark.colors.textDark,
  },
  userInfoWithImage: {
    flexDirection: 'row',
    gap: 15,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  userInfo: {
    gap: 4,
  },
});

export default styles;
