import React, { useCallback } from 'react';
import { closePopover } from 'molecules/Popover/Popover';
import { TouchableOpacity, View } from 'react-native';
import { Text } from '@rneui/themed';
import styles from './FriendCardOptionsPopover.style';
import { FriendCardOptionsPopoverProps } from './types';

const FriendCardOptionsPopover: React.FC<FriendCardOptionsPopoverProps> = (
  props,
) => {
  const { onRemoveFriendPressed } = props;

  const handleRemoveFriend = useCallback(() => {
    onRemoveFriendPressed?.();
    closePopover?.();
  }, [onRemoveFriendPressed]);

  return (
    <TouchableOpacity onPress={handleRemoveFriend}>
      <View style={styles.popoverContent}>
        <Text style={styles.popoverText}>Remove Friend</Text>
      </View>
    </TouchableOpacity>
  );
};

export default React.memo(FriendCardOptionsPopover);
