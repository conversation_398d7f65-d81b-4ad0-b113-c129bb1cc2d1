import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    height: 500,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 80,
    marginBottom: 12,
  },
  description: {
    fontFamily: 'Montserrat-600',
    fontSize: 14,
    color: dark.colors.textLight,
  },
  refreshButton: {
    fontFamily: 'Montserrat-600',
    fontSize: 14,
    color: dark.colors.primary,
    marginTop: 12,
  },
});

export default styles;
