import { View, Text, Image } from 'react-native';
import React from 'react';
import styles from './NoPendingFriendRequests.style';

const pookieImage = require('@/assets/images/pookie/no_friends_pookie.png');

const NoPendingFriendRequests = () => (
  <View style={styles.container}>
    <Image source={pookieImage} style={styles.image} />
    <Text style={styles.description}>No Pending Friend Requests</Text>
  </View>
);

export default React.memo(NoPendingFriendRequests);
