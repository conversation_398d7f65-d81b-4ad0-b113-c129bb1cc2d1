import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useMediaQuery from 'core/hooks/useMediaQuery';
import USER_ACTIVITY from '@/src/core/constants/userActivityConstants';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import userReader from 'core/readers/userReader';
import useChatStore from '@/src/store/useChatStore';
import Analytics from 'core/analytics/index';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import dark from 'core/constants/themes/dark';
import useGetMessageGroupForFriends from '../../hooks/queries/useGetMessageGroupForFriends';
import { FriendCardProps } from './types';
import styles from './FriendCard.style';

const FriendCard: React.FC<FriendCardProps> = (props) => {
  const { infoData, isOnline, currActivity, pageName } = props;
  const { isMobile: isCompactMode } = useMediaQuery();
  const { user: currentUser } = useSession();
  const router = useRouter();
  const [messageGroupLoading, setMessageGroupLoading] = useState(false);
  const cardRef = useRef<View>(null);

  const { refreshMessageGroups } = useChatStore((state) => ({
    refreshMessageGroups: state.refreshMessageGroups,
  }));

  const [overlayStyle, setOverlayStyle] = useState(styles.overlayStyle);

  const onCardLayoutChange = useCallback(() => {
    cardRef.current?.measure((x, y, width, height, pageX, pageY) => {
      setOverlayStyle([
        styles.overlayStyle,
        { top: pageY, right: isCompactMode ? pageX : pageX + 180 },
      ] as any);
    });
  }, [cardRef, setOverlayStyle, isCompactMode]);

  const { getMessageGroupIDForFriends } = useGetMessageGroupForFriends();

  const userDetailsData = infoData?.friendInfo;

  const {
    username,
    profileImageUrl,
    rating,
    _id: senderId,
    name,
  } = userDetailsData ?? {};

  const navigateToUserProfile = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
        .CLICKED_ON_USER_PROFILE_FROM_FRIENDS_TAB,
    );
    Analytics.track(
      `${pageName}: ${ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICK_ON_PROFILE_PICTURE}`,
    );
    router.push(`/profile/${username}`);
  }, [username, router]);

  const navigateToCreateLobby = useCallback(() => {
    router.push(`/games/lobby?friendId=${senderId}`);
  }, [senderId, router]);

  const routeToChat = useCallback(async () => {
    setMessageGroupLoading(true);
    try {
      const { data } = await getMessageGroupIDForFriends({
        friendID: senderId,
      });
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_USER_CHAT_ACTION,
        {
          currentUserId: userReader.id(currentUser),
          friendUserId: senderId,
        },
      );
      Analytics.track(
        `${pageName}: ${ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICK_ON_MESSAGE_FRIENDS}`,
      );
      await refreshMessageGroups();
      setMessageGroupLoading(false);
      router.push(`/chat?groupId=${data.getMessageGroupIdForFriends}`);
    } catch (e) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong. Please try again later`,
      });
    } finally {
      setMessageGroupLoading(false);
    }
  }, [
    currentUser,
    getMessageGroupIDForFriends,
    pageName,
    refreshMessageGroups,
    router,
    senderId,
  ]);

  const isExploring = useMemo(
    () => currActivity === USER_ACTIVITY.EXPLORING,
    [currActivity],
  );

  return (
    <View style={styles.container} ref={cardRef} onLayout={onCardLayoutChange}>
      <TouchableOpacity
        style={styles.userInfoWithImage}
        onPress={navigateToUserProfile}
      >
        <View style={styles.userImageContainer}>
          <Image
            source={{ uri: profileImageUrl }}
            resizeMode="cover"
            style={styles.userImage}
          />
          {isOnline && (
            <View
              style={[
                styles.statusIcon,
                !isExploring && {
                  backgroundColor: dark.colors.inGameIndicator,
                },
              ]}
            />
          )}
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName} numberOfLines={1}>
            {username}
          </Text>
          <Text style={styles.userRating}>{`${name} `}</Text>
        </View>
      </TouchableOpacity>

      <View style={[styles.challengeContainer]}>
        {isOnline && (
          <InteractivePrimaryButton
            onPress={navigateToCreateLobby}
            label="Challenge"
            buttonStyle={styles.findFriendButton}
            buttonContainerStyle={styles.findFriendButtonContianer}
            labelStyle={styles.findFriendText}
            buttonBorderBackgroundStyle={styles.findFriendBackground}
            buttonContentStyles={{ paddingVertical: 0 }}
          />
        )}
        <MaterialIcons
          name="chat-bubble-outline"
          size={18}
          color={dark.colors.textDark}
          onPress={routeToChat}
        />
      </View>
    </View>
  );
};

export default React.memo(FriendCard);
