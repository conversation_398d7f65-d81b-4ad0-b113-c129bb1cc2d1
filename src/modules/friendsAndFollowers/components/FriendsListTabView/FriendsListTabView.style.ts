import { Dimensions, StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    tabBarContainer: {
      paddingBottom: isCompactMode ? 16 : 20,
      //   maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
      width: '100%',
      alignSelf: 'flex-start',
      justifyContent: 'space-between',
    },
    tabBar: {
      width: '100%',
      maxWidth: 960,
      backgroundColor: dark.colors.background,
      borderBottomColor: dark.colors.tertiary,
      borderBottomWidth: isCompactMode ? 1 : 2,
    },
    indicator: {
      height: 4,
      justifyContent: 'center',
      borderTopRightRadius: 5,
      borderTopLeftRadius: 5,
      backgroundColor: dark.colors.secondary,
      // marginHorizontal:12
    },
    tabStyle: {
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
      flex: 1,
    },
    expandedTabStyle: {
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
      width: 120,
    },
    label: {
      fontFamily: 'Montserrat-400',
      fontSize: 14,
      lineHeight: 20,
      textAlign: 'center',
    },
    tabMainContainer: {
      flex: 1,
      height: '100%',
      width: isCompactMode
        ? Dimensions.get('window').width
        : Dimensions.get('window').width * 0.5,
    },
    pendingCount: {
      height: 16,
      borderRadius: 4,
      backgroundColor: dark.colors.skyBlueBg,
    },
    pendingCountText: {
      fontFamily: 'Montserrat-700',
      lineHeight: 16,
      fontSize: 12,
      textAlign: 'center',
      paddingHorizontal: 5,
      color: dark.colors.primary,
    },
    tabBarLabelRow: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: 6,
    },
  });

const useFriendsListTabViewStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useFriendsListTabViewStyles;
