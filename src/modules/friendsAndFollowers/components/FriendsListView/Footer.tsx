import React, { useCallback } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import { useRouter } from 'expo-router';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

const styles = StyleSheet.create({
  playWithLinkButtonContainer: {
    borderRadius: 20,
    width: 100,
    height: 40,
  },
  playWithLinkText: {
    fontSize: 12,
  },
  searchContainer: {
    borderColor: dark.colors.placeholder,
    borderWidth: 1,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  searchContent: {
    gap: 8,
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchText: {
    color: dark.colors.placeholder,
    fontFamily: 'Montserrat-600',
    fontSize: 12,
  },
});

const Footer = ({pageName}) => {
  const router = useRouter();

  const onPressPlayViaLink = useCallback(() => {
    Analytics.track(`${pageName}: ${ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_SHARE_LINK}`)
    router.push(`/games/lobby`);
  }, [router]);

  const onPressSearchBox = useCallback(() => {
    Analytics.track(`${pageName}: ${ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_SEARCH_MATHELETE}`)
    router.push(`/search-mathletes`);
  }, [router]);

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 12,
        paddingVertical: 12,
      }}
    >
      <InteractiveSecondaryButton
        onPress={onPressPlayViaLink}
        label="Link"
        iconConfig={{ name: 'link-2', type: ICON_TYPES.FEATHER }}
        labelStyle={styles.playWithLinkText}
        buttonContainerStyle={styles.playWithLinkButtonContainer}
      />
      <Pressable onPress={onPressSearchBox} style={styles.searchContainer}>
        <View style={styles.searchContent}>
          <FontAwesome
            name="search"
            size={16}
            color={dark.colors.placeholder}
          />
          <Text style={styles.searchText}>Search for mathletes</Text>
        </View>
      </Pressable>
    </View>
  );
};

export default React.memo(Footer);
