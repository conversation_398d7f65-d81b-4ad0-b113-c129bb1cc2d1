import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Animated,
  Dimensions,
  ImageBackground,
  Platform,
  View,
} from 'react-native';
import { Text } from '@rneui/themed';
import Carousel from 'react-native-reanimated-carousel';
import _map from 'lodash/map';
import dark from 'core/constants/themes/dark';
import Rive from 'atoms/Rive';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';

import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';
import GoogleLoginButton from 'core/oauth/components/GoogleLoginButton';
import AppleLogin from 'core/oauth/components/AppleLogin';
import { useRouter } from 'expo-router';
import OfflineScreen from 'shared/OfflineScreen';
import useNetworkStatus from 'core/hooks/useNetworkStatus';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import BackgroundImage from '@/assets/images/backgrounds/background_blocks.png';
import styles from './SplashScreen.style';
import { splashScreenData } from './constants/SplashScreenData';

configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false,
});

const { height, width } = Dimensions.get('window');

const SPLASH_ANIMATION_DURATION = 2000;

const SplashScreen = () => {
  const isWeb = Platform.OS === 'web';
  const [activeIndex, setActiveIndex] = useState(0);
  const carouselRef = useRef(null);
  const progressValue = useRef(new Animated.Value(0)).current;
  const dotAnimatedValues = useRef(
    splashScreenData.map(() => new Animated.Value(0)),
  ).current;
  const dotAnimations = useRef([]);
  const [isAutoScrolling, setIsAutoScrolling] = useState(true);
  const router = useRouter();
  const { isNetworkReachable, isMatiksReachable } = useNetworkStatus();

  const setActivePageIndex = useCallback((index) => {
    if (index === splashScreenData.length - 1) {
      setIsAutoScrolling(false);
    }
    setActiveIndex(index);
  }, []);

  const renderStyledContent = useCallback((content) => {
    if (typeof content === 'string') {
      return <Text style={styles.titleText}>{content}</Text>;
    }

    return (
      <Text style={styles.titleText}>
        {_map(content, (segment, index) => (
          <Text
            key={index}
            style={[
              segment.style || styles.titleText,
              segment.color ? { color: segment.color } : {},
            ]}
          >
            {segment.text}
          </Text>
        ))}
      </Text>
    );
  }, []);

  useEffect(() => {
    const duration = SPLASH_ANIMATION_DURATION;
    dotAnimatedValues.forEach((value, index) => {
      dotAnimations.current[index]?.stop();

      if (index === activeIndex) {
        value.setValue(0);
        dotAnimations.current[index] = Animated.timing(value, {
          toValue: 1,
          duration,
          useNativeDriver: false,
        });
        dotAnimations?.current[index].start();
      } else {
        value.setValue(0);
      }
    });
    return () => {
      dotAnimations?.current.forEach((animation) => animation?.stop());
    };
  }, [activeIndex, dotAnimatedValues]);

  const renderDots = useCallback(() => {
    const dotCount = splashScreenData.length;
    return (
      <View style={styles.dotsContainer}>
        {_map(splashScreenData, (_, index) => {
          let sizeInputRange;
          if (index === 0) {
            sizeInputRange = [-1, 0, 1];
          } else if (index === dotCount - 1) {
            sizeInputRange = [dotCount - 2, dotCount - 1, dotCount];
          } else {
            sizeInputRange = [index - 1, index, index + 1];
          }
          const animatedWidth = progressValue.interpolate({
            inputRange: sizeInputRange,
            outputRange: [
              styles.dot.width,
              styles.activeDot.width,
              styles.dot.width,
            ],
            extrapolate: 'clamp',
          });

          const fillWidth = dotAnimatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: ['0%', '100%'],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              key={`dot-outer-${index}`}
              style={[styles.dotContainer, { width: animatedWidth }]}
            >
              <Animated.View style={[styles.dotFill, { width: fillWidth }]} />
            </Animated.View>
          );
        })}
      </View>
    );
  }, [progressValue, dotAnimatedValues]);

  const riveAnimationHeight = Math.min(
    295,
    Dimensions.get('window').height * 0.4,
  );

  const renderItem = useCallback(
    ({ item, index }) => (
      <View
        key={`carousel-item-${index}`}
        style={{
          width,
          alignItems: 'center',
          justifyContent: 'center',
          height: height * 0.5,
        }}
      >
        <Rive
          url={item.animationLink}
          autoPlay
          style={{ width: 300, height: riveAnimationHeight }}
        />
        <View style={styles.textContainer}>
          {renderStyledContent(item.title)}
        </View>
      </View>
    ),
    [renderStyledContent],
  );

  const renderButtonComponent = useCallback(
    () => (
      <View style={styles.googleButtonComponentContainer}>
        <FontAwesome6 name="google" size={13} color={dark.colors.textDark} />
        <Text style={styles.googleButtonText}>CONTINUE WITH GOOGLE</Text>
      </View>
    ),
    [],
  );

  const onProgressChange = useCallback(
    (_, absoluteProgress) => {
      progressValue.setValue(absoluteProgress);
      const currentFloorIndex = Math.floor(absoluteProgress);
      setActivePageIndex(currentFloorIndex % splashScreenData.length);
    },
    [setActivePageIndex, progressValue],
  );

  const redirectToFeaturesPreview = useCallback(() => {
    router.push('/features-preview');
  }, [router]);

  const renderNewUserGetStartedButton = useCallback(
    () => (
      <View style={{ width: '100%' }}>
        <InteractivePrimaryButton
          label="NEW USER? GET STARTED"
          radius={20}
          buttonContainerStyle={styles.newUserGetStartedButton}
          buttonStyle={styles.newUserGetStartedButtonStyle}
          labelStyle={styles.newUserGetStartedLabel}
          onPress={redirectToFeaturesPreview}
          buttonBorderBackgroundStyle={styles.newUserGetStartedButtonBackground}
        />
      </View>
    ),
    [redirectToFeaturesPreview],
  );

  if (!isNetworkReachable || !isMatiksReachable) {
    return (
      <OfflineScreen
        isMatiksReachable={isMatiksReachable}
        isNetworkReachable={isNetworkReachable}
      />
    );
  }

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          paddingTop: 42,
        }}
      >
        <Text style={styles.matiksText}>MATIKS</Text>
      </View>
      <ImageBackground
        style={{
          flex: 1,
          height: '100%',
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        imageStyle={[
          {
            opacity: 0.5,
          },
          {
            transform: [{ rotateX: '-900deg' }, { rotateY: '0deg' }],
          },
        ]}
        source={BackgroundImage}
        resizeMode="stretch"
      >
        <Carousel
          ref={carouselRef}
          loop
          width={width}
          height={height * 0.5}
          data={splashScreenData}
          scrollAnimationDuration={1000}
          renderItem={renderItem}
          autoPlay={isAutoScrolling || isWeb || !isWeb}
          onProgressChange={onProgressChange}
          autoPlayInterval={2000}
        />
        {renderDots()}
      </ImageBackground>

      <View
        style={{
          width: '100%',
          gap: 12,
          justifyContent: 'center',
          paddingBottom: 15,
          paddingHorizontal: 16,
        }}
      >
        {renderNewUserGetStartedButton()}
        <View style={styles.orContainer}>
          <View style={styles.orSeparator} />
          <Text style={styles.orText}>OR</Text>
          <View style={styles.orSeparator} />
        </View>
        <GoogleLoginButton renderButtonComponent={renderButtonComponent} />
        {Platform.OS === 'ios' && (
          <View style={{ marginTop: 16, marginBottom: 12 }}>
            <AppleLogin />
          </View>
        )}
      </View>
    </View>
  );
};

export default React.memo(SplashScreen);
