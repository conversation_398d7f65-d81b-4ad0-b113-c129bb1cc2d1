import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  contentContainer: { 
    marginBottom: 60, 
    alignItems: 'center' 
  },
  titleIcon: { 
    flexDirection: 'row' 
  },
  contentTitle: {
    fontSize: 20,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    textAlign: 'center',
    fontWeight: '600',
    marginLeft: 5,
  },
  taglineText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    letterSpacing: 2,
    color: dark.colors.textLight,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default styles;
