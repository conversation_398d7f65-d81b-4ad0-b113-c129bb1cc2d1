import React, { useCallback } from 'react';
import { View } from 'react-native';

import BackButton from '@/src/components/molecules/BackButton';
import Rive from 'atoms/Rive';
import _get from 'lodash/get';
import styles from './RiveAnimationContainer.style';
import { RiveAnimationProps } from '../OnboardingDetails/types';

const RiveAnimationContainer: React.FC<RiveAnimationProps> = ({
  riveRef,
  currentStep,
  screenData,
  alreadyLogin,
  showBackButton,
  onBackPress,
}) => {
  const getCurrentResourceName = useCallback(() => {
    if (alreadyLogin) {
      return 'crash';
    }
    if (currentStep === 1 && screenData.length !== 1) {
      return 'duels';
    }
    return _get(screenData, [currentStep - 1, 'resourceName']);
  }, [alreadyLogin, currentStep, screenData]);

  return (
    <View style={styles.riveContainer}>
      <Rive
        resourceName={getCurrentResourceName()}
        riveRef={riveRef}
        stateMachineName="State Machine 1"
        autoPlay
        style={{
          width: '175%',
          height: '100%',
        }}
      />
      <View style={{ position: 'absolute', left: 0, top: 20 }}>
        {showBackButton && (
          <BackButton
            buttonCotainerStyle={styles.backButtonContainerStyle}
            labelStyle={styles.backButtonLabelStyle}
            label="PREVIOUS"
            onBackPress={onBackPress}
          />
        )}
      </View>
    </View>
  );
};

export default React.memo(RiveAnimationContainer);
