import Svg, { Clip<PERSON><PERSON>, Defs, G, <PERSON>, Path, Rect } from 'react-native-svg';

const MatiksLogoSvg = () => {
  return (
    <Svg width={25} height={25} viewBox="0 0 80 80" style={{ marginBottom: 8 }}>
      <Defs>
        <ClipPath id="clip0_194_2551">
          <Rect width="80" height="80" fill="white" />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clip0_194_2551)">
        <Mask
          id="mask0_194_2551"
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="80"
          height="80"
        >
          <Rect width="80" height="80" fill="#D9D9D9" />
        </Mask>
        <G mask="url(#mask0_194_2551)">
          <Path
            d="M26.6663 73.3333L29.9997 50H13.333L43.333 6.66663H49.9997L46.6663 33.3333H66.6663L33.333 73.3333H26.6663Z"
            fill="#E3FFA8"
          />
        </G>
      </G>
    </Svg>
  );
};

export default MatiksLogoSvg;
