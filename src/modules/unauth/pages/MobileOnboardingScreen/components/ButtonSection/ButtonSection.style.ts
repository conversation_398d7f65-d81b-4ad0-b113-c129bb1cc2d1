import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  newUserGetStartedButton: {
    width: '100%',
    minWidth: 200,
    justifyContent: 'center',
    alignItems: 'center',
    height: 60,
    borderRadius: 12,
  },
  newUserGetStartedButtonStyle: {
    borderWidth: 0.8,
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    height: 60,
  },
  newUserGetStartedLabel: {
    fontWeight: '700',
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    letterSpacing: 2,
    color: 'white',
  },
  newUserGetStartedButtonBackground: {
    flex: 1,
    backgroundColor: dark.colors.victoryColor,
  },
  alreadyUserBtn: {
    backgroundColor: 'transparent',
    height: 60,
    opacity: 0.6,
    marginTop: 20,
  },
  alreadyUserTxt: {
    fontWeight: '700',
    fontFamily: 'Montserrat-700',
    fontSize: 12,
  },
  nextButtonContainer: {
    width: '100%',
    zIndex: 200,
    marginBottom: 20,
    position: 'relative',
  },
  progressDotsContainer: {
    position: 'absolute',
    right: 20,
    top: '40%',
  },
  getStartedButton: { width: '100%' },
  nextButton: {
    paddingHorizontal: 20,
  },
  appleLoginButton: { marginTop: 20, height: 60 },
  alreadyUserButton: { zIndex: 100 }
});

export default styles;
