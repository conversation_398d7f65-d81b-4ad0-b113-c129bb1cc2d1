import OnboardingDetails, {
  ButtonType,
} from '@/src/modules/unauth/pages/MobileOnboardingScreen/components/OnboardingDetails/OnboardingDetails';
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';

import React, { useCallback, useState } from 'react';
import { onboardingScreenData } from '@/src/modules/unauth/pages/SplashScreen/constants/SplashScreenData';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import {
  hideToast,
  showToast,
  TOAST_TYPE,
} from '@/src/components/molecules/Toast';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { PAGE_NAMES } from '@/src/core/constants/pageNames';

configureReanimatedLogger({ level: ReanimatedLogLevel.warn, strict: false });

const DuelLandingScreen = () => {
  const router = useRouter();
  const param = useLocalSearchParams();
  const [guestUserLoading, setGuestUserLoading] = useState(false);

  const nextonPress = useCallback(
    (loggedInUser?: any) => {
      try {
        if (loggedInUser) {
          const { isSignup = false, isGuest = false } = loggedInUser;
          if (isSignup && !isGuest) {
            router.replace('/set-username');
          } else {
            router.replace('/home');
          }
        }
      } catch (error) {}
    },
    [router],
  );

  const { createGuestUser } = useSession();

  const loginAsGuest = useCallback(async () => {
    showToast({ type: TOAST_TYPE.LOADING, description: 'Logging in...' });
    Analytics.track(ANALYTICS_EVENTS.ONBOARDING.CLICKED_ON_GUEST_LOGIN, {
      pageName: PAGE_NAMES.ONBOARDING_FEATURES_PREVIEWS,
    });
    try {
      setGuestUserLoading(true);
      const response = await createGuestUser();
      hideToast();
      router.replace({
        pathname: '/home',
        params: {
          userData: JSON.stringify(response),
          isSignup: 'false',
          isGuest: 'true',
        },
      });
    } catch (error) {
      console.error('Error creating guest user:', error);
    } finally {
      setGuestUserLoading(false);
    }
  }, [createGuestUser, router]);

  return (
    <OnboardingDetails
      riveResourceName="duels"
      riveStateMachine="State Machine 1"
      riveBooleanState="proceed"
      showBackButton
      buttonType={ButtonType.NEXT}
      nextOnPress={nextonPress}
      screenData={onboardingScreenData}
      loginAsGuest={loginAsGuest}
      guestUserLoading={guestUserLoading}
      alreadyLogin={!!param?.alreadyLogin}
    />
  );
};

export default React.memo(DuelLandingScreen);
