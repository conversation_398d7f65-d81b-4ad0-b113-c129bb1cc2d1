import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    width: '90%',
    marginHorizontal: '10%',
  },
  title: {
    color: dark.colors.textLight,
    fontSize: 20,
    fontFamily: 'Montserrat-700',
    maxWidth: '80%',
  },
  notPlayedContainer: {
    backgroundColor: dark.colors.triggerPointborder,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 80,
    marginVertical: 10,
    gap: 20,
  },
  notPlayed: {
    color: dark.colors.demotionText,
    fontFamily: 'Montserrat-700',
    fontSize: 13,
  },
  emptyText: {
    color: dark.colors.textLight,
    fontSize: 14,
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Montserrat-500',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    width: '100%',
    paddingVertical: 18,
    paddingLeft: 10,
    paddingRight: 24,
    borderBottomWidth: 1,
    alignItems: 'center',
    borderBottomColor: dark.colors.primary,
  },
  rank: {
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
  },
  name: {
    textAlign: 'left',
    color: dark.colors.textLight,
    maxWidth: 140,
    flex: 1,
    fontFamily: 'Montserrat-600',
    fontSize: 12,
  },
  score: {
    color: dark.colors.textLight,
    flex: 1,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
    textAlign: 'right',
  },
  time: {
    flex: 2,
    textAlign: 'right',
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-700',
    fontSize: 14,
  },
  profileContainer: {
    flexDirection: 'row',
    width: '60%',
    gap: 16,
    alignItems: 'center',
  },
  detailsContainer: { justifyContent: 'center', height: 32 },
  ratingText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: withOpacity(dark.colors.textLight, 0.4),
  },
  paginationContainer: {
    backgroundColor: dark.colors.background,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 6,
    borderRadius: 10,
    bottom: 0,
    position: 'absolute',
    alignItems: 'center',
  },
  pageButton: {
    marginHorizontal: 5,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 10,
  },
  selectedPage: { backgroundColor: dark.colors.primary },
  pageText: { color: dark.colors.textDark, fontSize: 16 },
  selectedPageText: { color: dark.colors.secondary, fontSize: 16 },
  avatarContainer: { gap: 10, justifyContent: 'center', alignItems: 'center' },
  avatar: {
    width: 60,
    height: 60,
    borderWidth: 1,
    borderColor: dark.colors.streak,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarImage: { borderRadius: 50, width: 52, height: 52 },

  podiumContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 30,
  },
  topThreeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    alignItems: 'center',
  },
  podiumName: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
  },
  podiumScore: {
    fontSize: 10,
    textAlign: 'center',
    marginTop: 2,
    color: '#666',
  },
  badgeContainer: {
    position: 'absolute',
    height: 18,
    borderColor: dark.colors.streak,
    paddingHorizontal: 6,
    borderWidth: 1,
    bottom: -8,
    borderRadius: 20,
    backgroundColor: dark.colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    fontFamily: 'Montserrat-800',
    fontSize: 10,
    color: dark.colors.textLight,
  },
  scoreContainer: {
    height: 24,
    backgroundColor: withOpacity(dark.colors.textLight, 0.1),
    borderRadius: 8,
    paddingHorizontal: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreText: {
    fontFamily: 'Montserrat-600',
    fontSize: 10,
    color: dark.colors.textLight,
  },
  borderLine: {
    width: '100%',
    height: 2,
    backgroundColor: dark.colors.orange,
    bottom: 6,
  },
  bottomGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
  },
  rankContainer: {
    position: 'absolute',
    bottom: 30,
    height: 44,
    paddingHorizontal: 16,
    backgroundColor: '#242424',
    alignSelf: 'center',
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: withOpacity(dark.colors.textLight, 0.4),
    borderWidth: 0.5,
    zIndex: 100,
  },
  rankCircle: {
    width: 28,
    height: 28,
    borderRadius: 32,
    borderColor: dark.colors.streakOrangeColor,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  indexContainer: { width: 60, alignItems: 'center', justifyContent: 'center' },
  rankStyle: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    alignSelf: 'center',
    color: withOpacity(dark.colors.textLight, 0.9),
  },
  tabBar: { width: '100%', backgroundColor: 'transparent' },
  indicator: {
    height: 2,
    justifyContent: 'center',
    backgroundColor: dark.colors.puzzle.primary,
  },
  currentFormatedTime: {
    borderWidth: 0,
    borderRadius: 50,
    borderColor: dark.colors.secondary,
    padding: 8,
  },
  tabBarLabel: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: dark.colors.textLight,
    letterSpacing: 1,
  },
});

export default styles;
