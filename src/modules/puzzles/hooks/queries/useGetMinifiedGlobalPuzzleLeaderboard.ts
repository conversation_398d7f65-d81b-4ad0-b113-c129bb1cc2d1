import { gql, useLazyQuery, WatchQueryFetchPolicy } from '@apollo/client';
import { USER_PUBLIC_DETAIL_FRAGMENT } from '@/src/core/graphql/fragments/userPublicDetail';
import _get from 'lodash/get';

export const GET_MINIFIED_PUZZLE_LEADERBOARD = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetMinifiedGlobalPuzzleLeaderboardById($puzzleId: ID!) {
    getMinifiedGlobalPuzzleLeaderboardById(puzzleId: $puzzleId) {
      totalParticipants
      participants {
        timeSpent
        rank
        user {
          ...UserPublicDetailFields
        }
      }
    }
  }
`;

const useGetMinifiedGlobalPuzzleLeaderboard = ({
  puzzleId,
  fetchPolicy = 'cache-first',
}: {
  puzzleId: string;
  fetchPolicy?: WatchQueryFetchPolicy;
}) => {
  const [fetchMinifiedGlobalPuzzleLeaderboardQuery, { loading, error, data }] =
    useLazyQuery(GET_MINIFIED_PUZZLE_LEADERBOARD, {
      errorPolicy: 'all',
      fetchPolicy,
      variables: {
        puzzleId,
      },
    });
  return {
    fetchMinifiedGlobalPuzzleLeaderboardQuery,
    leaderboard: _get(data, 'getMinifiedGlobalPuzzleLeaderboardById'),
    loading,
    error,
  };
};

export default useGetMinifiedGlobalPuzzleLeaderboard;
