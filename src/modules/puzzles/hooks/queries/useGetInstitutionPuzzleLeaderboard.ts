import { useCallback, useState, useMemo } from 'react';
import { gql, useLazyQuery, WatchQueryFetchPolicy } from '@apollo/client';
import _get from 'lodash/get';
import _size from 'lodash/size';
import { USER_PUBLIC_DETAIL_FRAGMENT } from '@/src/core/graphql/fragments/userPublicDetail';

export const GET_INSTITUTION_PUZZLE_LEADERBOARD = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetInstitutionPuzzleLeaderboardById(
    $puzzleId: ID!
    $offset: Int
    $limit: Int
  ) {
    getInstitutionPuzzleLeaderboardById(
      puzzleId: $puzzleId
      offset: $offset
      limit: $limit
    ) {
      totalParticipants
      participants {
        timeSpent
        rank
        user {
          ...UserPublicDetailFields
        }
      }
    }
  }
`;

const DEFAULT_PAGE_SIZE = 20;

interface Params {
  offset: number;
  hasMore: boolean;
}

const useGetInstitutionLeaderboardById = ({
  puzzleId,
  fetchPolicy = 'cache-first',
  pageSize = DEFAULT_PAGE_SIZE,
}: {
  puzzleId: string;
  fetchPolicy?: WatchQueryFetchPolicy;
  pageSize?: number;
}) => {
  const [participants, setParticipants] = useState<any[]>([]);
  const [totalParticipants, setTotalParticipants] = useState(0);

  const [params, setParams] = useState<Params>({
    offset: 0,
    hasMore: true,
  });

  const [fetchLeaderboardQuery, { loading, error }] = useLazyQuery(
    GET_INSTITUTION_PUZZLE_LEADERBOARD,
    {
      notifyOnNetworkStatusChange: true,
      fetchPolicy,
    },
  );

  const fetchLeaderboard = useCallback(
    async (currentOffset: number) => {
      if (loading) {
        return;
      }
      try {
        const response = await fetchLeaderboardQuery({
          variables: {
            puzzleId,
            offset: currentOffset,
            limit: pageSize,
          },
        });

        const { data } = response ?? {};
        const leaderboardData = _get(
          data,
          'getInstitutionPuzzleLeaderboardById',
        );
        const newParticipants = _get(leaderboardData, 'participants');
        const total = _get(leaderboardData, 'totalParticipants');

        if (_size(newParticipants) === 0) {
          if (currentOffset === 0) {
            setParticipants([]);
          }
          setParams((prevParams) => ({
            ...prevParams,
            hasMore: false,
          }));
          return;
        }

        setTotalParticipants(total || 0);

        setParticipants((prevParticipants) => {
          if (currentOffset === 0) {
            return newParticipants || [];
          } else {
            return [...prevParticipants, ...(newParticipants || [])];
          }
        });

        const currentTotal =
          currentOffset === 0
            ? _size(newParticipants)
            : participants.length + _size(newParticipants);

        const hasMoreData = currentTotal < (total || 0);

        setParams({
          offset: currentOffset + pageSize,
          hasMore: hasMoreData,
        });
      } catch (err) {
        console.error('Error fetching leaderboard data:', err);
        setParams((prevParams) => ({
          ...prevParams,
          hasMore: false,
        }));
      }
    },
    [
      fetchLeaderboardQuery,
      loading,
      puzzleId,
      pageSize,
      participants.length,
    ],
  );

  const loadMore = useCallback(() => {
    if (loading || !params.hasMore) return;
    fetchLeaderboard(params.offset);
  }, [fetchLeaderboard, loading, params.hasMore, params.offset]);

  const leaderboardData = useMemo(() => {
    return participants;
  }, [participants]);

  const resetLeaderboard = useCallback(() => {
    setParticipants([]);
    setTotalParticipants(0);
    setParams({
      offset: 0,
      hasMore: true,
    });
  }, []);

  return {
    loading,
    error,
    participants: leaderboardData,
    totalParticipants,
    loadMore,
    hasMore: params.hasMore,
    isLeaderboardEmpty: _size(participants) === 0,
    fetchInstitutionLeaderboardById: () => fetchLeaderboard(0),
    resetLeaderboard,
  };
};

export default useGetInstitutionLeaderboardById;
