import { gql, useLazyQuery, WatchQueryFetchPolicy } from '@apollo/client';
import _get from 'lodash/get';
import { USER_PUBLIC_DETAIL_FRAGMENT } from '@/src/core/graphql/fragments/userPublicDetail';

export const GET_MINIFIED_FRIENDS_PUZZLE_LEADERBOARD = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetMinifiedFriendsPuzzleLeaderboardById($puzzleId: ID!) {
    getMinifiedFriendsPuzzleLeaderboardById(puzzleId: $puzzleId) {
      totalParticipants
      participants {
        timeSpent
        rank
        user {
          ...UserPublicDetailFields
        }
      }
    }
  }
`;

const useGetMinifiedFriendsPuzzleLeaderboard = ({
  puzzleId,
  fetchPolicy = 'cache-first',
}: {
  puzzleId: string;
  fetchPolicy?: WatchQueryFetchPolicy;
}) => {
  const [fetchMinifiedFriendsPuzzleLeaderboardQuery, { loading, error, data }] =
    useLazyQuery(GET_MINIFIED_FRIENDS_PUZZLE_LEADERBOARD, {
      fetchPolicy: fetchPolicy,
      notifyOnNetworkStatusChange: true,
      variables: {
        puzzleId,
      },
    });

  return {
    fetchMinifiedFriendsPuzzleLeaderboardQuery,
    leaderboard: _get(data, 'getMinifiedFriendsPuzzleLeaderboardById'),
    loading,
    error,
  };
};

export default useGetMinifiedFriendsPuzzleLeaderboard;
