import LeaderboardRow from '@/src/components/shared/LeaderboardRow';
import { View, Text } from 'react-native';
import React from 'react';
import RIVE_ANIMATIONS from '@/src/core/constants/riveAnimations';
import Rive from '@/src/components/atoms/Rive';
import styles from './MinifiedPuzzleLeaderboard.style';
import Loading from '@/src/components/atoms/Loading';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import { PuzzleTypes } from '@/src/modules/puzzles/types/puzzleType';
import { Participant } from '@/src/modules/puzzles/readers/puzzleLeaderboardReader';

interface MinifiedPuzzleLeaderboardProps {
  data: any;
  loading: boolean;
  puzzleType: PuzzleTypes;
}

const MinifiedPuzzleLeaderboard: React.FC<MinifiedPuzzleLeaderboardProps> = (
  props,
) => {
  const { data, loading, puzzleType } = props;

  const renderItem = ({
    item,
    index,
  }: {
    item: Participant;
    index: number;
  }) => {
    if (item.rank === 1) {
      return (
        <View style={{ marginTop: 20 }}>
          <View style={styles.firstPlaceContainer}>
            <Text style={styles.firstPlaceLabel}>BEST TIME</Text>
          </View>
          <Rive
            url={RIVE_ANIMATIONS.FIRST_PLACE_SHINE_ANIMATION}
            fit={'fill'}
            autoPlay
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              height: 80,
              width: '100%',
              zIndex: 1,
            }}
          />
          <LeaderboardRow
            index={index}
            participant={item}
            puzzleType={puzzleType}
          />
        </View>
      );
    }
    return (
      <LeaderboardRow
        index={index}
        participant={item}
        puzzleType={puzzleType}
      />
    );
  };

  if (loading) return <Loading />;

  return (
    <View>
      {_map(data, (item, index) => renderItem({ item, index }))}
      {_isEmpty(data) && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No Participants</Text>
        </View>
      )}
    </View>
  );
};

export default React.memo(MinifiedPuzzleLeaderboard);
