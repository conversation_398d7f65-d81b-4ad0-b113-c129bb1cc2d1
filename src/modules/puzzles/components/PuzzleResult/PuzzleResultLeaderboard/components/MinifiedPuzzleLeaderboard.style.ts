import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  emptyContainer: {
    paddingTop: 15,
    alignItems: 'center',
  },
  emptyText: {
    color: dark.colors.textLight,
    fontSize: 14,
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Montserrat-500',
  },
  firstPlaceContainer: {
    position: 'absolute',
    top: -10,
    right: 20,
    borderWidth: 1,
    borderColor: dark.colors.puzzle.primary,
    backgroundColor: dark.colors.background,
    borderRadius: 15,
    paddingHorizontal: 8,
    paddingVertical: 4,
    overflow: 'visible',
    zIndex: 2,
  },
  firstPlaceLabel: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
