import React, { useCallback, useEffect, useRef, useState } from 'react';
import GameStartTimer from 'modules/game/components/GameStartTimer';
import { PuzzleGame } from '../types/puzzleGame';

const usePuzzleGameWaitingTimer = ({ game }: { game: PuzzleGame }) => {
  const { startTime } = game;
  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTime();
  const timeDiff = startTimeDate.getTime() - currentTime;

  const [isReady, setIsReady] = useState(false);

  const prevTimeoutRef = useRef();

  useEffect(() => {
    if (prevTimeoutRef.current) {
      clearTimeout(prevTimeoutRef.current);
    }
    prevTimeoutRef.current = setTimeout(
      () => setIsReady(true),
      Math.max(0, timeDiff),
    );
    return () => {
      clearTimeout(prevTimeoutRef.current);
    };
  }, [timeDiff]);

  const renderWaitingTimer = useCallback(() => {
    if (timeDiff <= 0 || isReady) {
      return null;
    }

    return <GameStartTimer startTime={startTime} />;
  }, [timeDiff, isReady, startTime]);

  return {
    isReady,
    timeDiff,
    renderWaitingTimer,
  };
};

export default usePuzzleGameWaitingTimer;
