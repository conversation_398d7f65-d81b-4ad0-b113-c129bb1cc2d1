import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.tertiary,
    marginHorizontal: 20,
    gap: 13,
  },
  content: {
    gap: 4,
  },
  name: {
    color: dark.colors.textLight,
    fontSize: 15,
    fontFamily: 'Montserrat-600',
  },
  username: {
    color: dark.colors.textDark,
    fontSize: 13,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
