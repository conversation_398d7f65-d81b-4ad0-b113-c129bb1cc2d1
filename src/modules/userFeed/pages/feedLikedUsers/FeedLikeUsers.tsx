import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { UserPublicDetails } from '@/src/store/useFriendsStore/types';
import UserImage from '@/src/components/atoms/UserImage';
import Header from '@/src/components/shared/Header';
import userReader from '@/src/core/readers/userReader';
import Loading from '@/src/components/atoms/Loading';
import Pressable from '@/src/components/atoms/Pressable';
import ErrorView from 'atoms/ErrorView/ErrorView';
import styles from './FeedLikedUsers.style';
import useGetFeedLikedUsers from '../../hooks/useGetFeedLikedUsers';

const FeedLikedUsers = () => {
  const { id } = useLocalSearchParams();
  const { data, loading, error } = useGetFeedLikedUsers({
    feedId: id as string,
  });
  const router = useRouter();

  const onPress = useCallback(
    (userName: string) => {
      router.push(`/profile/${userName}`);
    },
    [router],
  );

  const renderItems = useCallback(
    ({ item }: { item: UserPublicDetails }) => (
      <Pressable
        onPress={() => onPress(userReader.username(item))}
        style={styles.container}
      >
        <UserImage user={item} size={52} />
        <View style={styles.content}>
          <Text style={styles.name}>{userReader.displayName(item)}</Text>
          <Text style={styles.username}>@{userReader.username(item)}</Text>
        </View>
      </Pressable>
    ),
    [onPress],
  );

  if (loading) return <Loading />;

  if (error) return <ErrorView errorMessage="Couldn't load feed reactions" />;

  return (
    <View style={{ flex: 1 }}>
      <Header title="Reactions" />
      <FlashList data={data} renderItem={renderItems} estimatedItemSize={52} />
    </View>
  );
};

export default React.memo(FeedLikedUsers);
