import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useMemo } from 'react';
import feedReader, { Feed } from '../../reader/feedReader';
import { View, Text } from 'react-native';
import { FallbackImage } from '@/src/components/atoms/helpers';
import { getFormattedDate } from '../../utils';
import SideImage from '../SideImage';
import Logo from 'assets/images/notificationCentre/logo.png';
import styles from './MultiFeedCard.style';
import CardFooter from '../CardFooter';

const MultiFeedCard = ({
  feedId,
  feedType,
  feed,
  imageUrl,
  isLiked,
}: {
  feedId: string;
  feedType: string;
  feed: Feed;
  imageUrl: string;
  isLiked: boolean;
}) => {
  const { userId } = useSession();
  const { title, description } = useMemo(() => {
    if (userId === feed?.sentFor) {
      return {
        title: feedReader.title(feed),
        description: feedReader.description(feed),
      };
    }

    return {
      title: feedReader.feedForFriendsTitle(feed),
      description: feedReader.feedForFriendsBody(feed),
    };
  }, [userId, feed]);
  return (
    <View style={[styles.container]}>
      <View style={styles.mainContainer}>
        <View style={styles.contentContainer}>
          <FallbackImage
            isMultiple={true}
            source={{
              uri: imageUrl,
            }}
            fallbackSource={Logo}
            style={styles.image}
          />
          <View style={styles.content}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.time}>
                {getFormattedDate(feedReader.sentAt(feed))}
              </Text>
            </View>
            <Text style={styles.description}>{description}</Text>
          </View>
        </View>
        <SideImage type={feedType} />
      </View>
      <View style={styles.buttonContainer}>
        <CardFooter
          feed={feed}
          isMultiFeed={true}
          title={title}
          feedType={feedType}
          isLiked={isLiked}
          feedId={feedId}
          likesCount={feedReader.likesCount(feed)}
        />
      </View>
    </View>
  );
};

export default MultiFeedCard;
