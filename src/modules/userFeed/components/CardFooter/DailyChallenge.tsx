import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useRouter } from 'expo-router';
import React from 'react';
import { useCallback } from 'react';
import Button from './Button';
import dark from '@/src/core/constants/themes/dark';
import { FEED_TYPES } from '@/src/core/constants/notificationTypes';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

interface PlayNowProps {
  type: string;
  isMultiFeed?: boolean;
}

const PlayNowComponent: React.FC<PlayNowProps> = React.memo((props) => {
  const { type, isMultiFeed } = props;
  const router = useRouter();
  const { user } = useSession();
  const onPress = useCallback(() => {
    if (type === FEED_TYPES.DAILY_CHALLENGE) {
      Analytics.track(ANALYTICS_EVENTS.FEEDS.CLICK_ON_BEAT_THAT, {
        dailyChallenge: FEED_TYPES.DAILY_CHALLENGE,
      });
      router.push(`/daily-challenge-leaderboard`);
    } else {
      Analytics.track(ANALYTICS_EVENTS.FEEDS.CLICK_ON_BEAT_THAT, {
        puzzle: type,
      });
      router.push(`puzzle/daily-challenge?puzzleType=${type}`);
    }
  }, [router, user]);

  return (
    <Button
      onPress={onPress}
      borderColor={dark.colors.d3ButtonBorderDark}
      label={'Solve Now!'}
      width={75}
    />
  );
});

const PlayNowContainer: React.FC<PlayNowProps> = (props) => {
  const { type } = props;

  if (!type || typeof type !== 'string' || type.trim() === '') {
    console.error('PlayNowContainer: Invalid type prop.');
    return null;
  }

  return <PlayNowComponent {...props} />;
};

export default React.memo(PlayNowContainer);
