import useStreakFreezer from '@/src/modules/profile/hooks/mutations/useStreakFreezer';
import Button from './Button';
import dark from '@/src/core/constants/themes/dark';
import useFeedStore from '@/src/store/useFeedStore';
import { useCallback } from 'react';
import _debounce from 'lodash/debounce';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import feedReader, { Feed } from '../../reader/feedReader';
import React from 'react';

interface ApplyShieldProps {
  feedId: string;
  feed: Feed;
}

const ApplyShieldComponent: React.FC<ApplyShieldProps> = (props) => {
  const { feed } = props;
  const { applyStreakFreezer, loading } = useStreakFreezer();
  const { refreshCurrentUser } = useSession();
  const feedDataId = feedReader.id(feed);
  const streakShieldApplied = feedReader.applyStreakShield(feed);

  const updateApplyStreakShield = useFeedStore(
    (state) => state.updateApplyStreakShield,
  );

  const onApplyPressed = useCallback(
    _debounce(async () => {
      await updateApplyStreakShield(feedDataId);
    }, 200),
    [feedDataId, updateApplyStreakShield],
  );

  const onPress = useCallback(async () => {
    try {
      await applyStreakFreezer();
      onApplyPressed();
    } catch (e) {
      console.error(e);
    } finally {
      refreshCurrentUser();
    }
  }, [applyStreakFreezer, onApplyPressed, refreshCurrentUser]);

  return (
    <Button
      onPress={onPress}
      label={
        loading ? 'APPLYING...' : streakShieldApplied ? 'APPLIED' : 'APPLY'
      }
      borderColor={dark.colors.d3ButtonBorderDark}
      width={100}
      disabled={streakShieldApplied || loading}
    />
  );
};

const ApplyShieldContainer: React.FC<ApplyShieldProps> = (props) => {
  const { feedId, feed } = props;

  if (!feedId || typeof feedId !== 'string' || feedId.trim() === '') {
    console.error('ApplyShieldContainer: Invalid feedId prop.');
    return null;
  }

  if (!feed || typeof feed !== 'object' || Object.keys(feed).length === 0) {
    console.error('ApplyShieldContainer: Invalid feed prop.');
    return null;
  }

  return <ApplyShieldComponent {...props} />;
};

export default React.memo(ApplyShieldContainer);
