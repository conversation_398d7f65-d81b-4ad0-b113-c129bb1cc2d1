/* eslint-disable class-methods-use-this */
import { ApolloClient, ApolloQueryResult, gql } from '@apollo/client';

declare global {
  function getApolloClient(): ApolloClient<object>;
}

/**
 * API class for Explored Features
 *
 * IMPORTANT: This API bypasses GraphQL cache completely using fetchPolicy: 'no-cache'
 * All data caching and state management is handled by Zustand store only.
 */

const GET_EXPLORED_FEATURES = gql`
  query GetExploredFeatures {
    getExploredFeatures {
      _id
      userId
      exploredFeatures {
        featureType
        exploredType
        timestamp
      }
      createdAt
      updatedAt
      needToShowExploreFeatures
    }
  }
`;

const IS_FEATURE_EXPLORED = gql`
  query IsFeatureExplored($featureType: FeatureType!) {
    isFeatureExplored(featureType: $featureType)
  }
`;

const MARK_FEATURE_AS_EXPLORED = gql`
  mutation MarkFeatureAsExplored($input: MarkFeatureAsExploredInput!) {
    markFeatureAsExplored(input: $input)
  }
`;

export interface ExploredFeaturesApiInterface {
  fetchExploredFeatures: () => Promise<ApolloQueryResult<any>>;
  isFeatureExplored: (featureType: string) => Promise<
    | ApolloQueryResult<any>
    | {
        data: { isFeatureExplored: boolean };
      }
  >;
  markFeatureAsExplored: (featureType: string) => Promise<any>;
}

export default class ExploredFeaturesApi
  implements ExploredFeaturesApiInterface
{
  async fetchExploredFeatures() {
    const apolloClient: ApolloClient<object> = getApolloClient();
    return apolloClient.query({
      query: GET_EXPLORED_FEATURES,
      fetchPolicy: 'no-cache', // Bypass Apollo cache completely
      errorPolicy: 'all',
    });
  }

  async isFeatureExplored(featureType: string) {
    const apolloClient: ApolloClient<object> = getApolloClient();
    if (!featureType) {
      return { data: { isFeatureExplored: false } };
    }
    return apolloClient.query({
      query: IS_FEATURE_EXPLORED,
      variables: { featureType },
      fetchPolicy: 'no-cache', // Bypass Apollo cache completely
      errorPolicy: 'all',
    });
  }

  async markFeatureAsExplored(featureType: string) {
    const apolloClient: ApolloClient<object> = getApolloClient();
    return apolloClient.mutate({
      mutation: MARK_FEATURE_AS_EXPLORED,
      variables: {
        input: {
          featureType,
        },
      },
      fetchPolicy: 'no-cache', // Bypass Apollo cache completely
      errorPolicy: 'all',
      // No cache update needed since we're not using Apollo cache
    });
  }
}
