export interface ExploredFeature {
  featureType: string;
  exploredType: string;
  timestamp: string;
}

export interface ExploredFeaturesData {
  _id: string;
  userId: string;
  exploredFeatures: ExploredFeature[];
  createdAt: string;
  updatedAt: string;
}

export interface ModuleProgress {
  explored: number;
  total: number;
  percentage: number;
}

export interface ExploredFeaturesState {
  // Data
  exploredFeaturesData: ExploredFeaturesData | null;
  exploredFeaturesMap: Record<string, boolean>;
  needToShowExploreFeatures: boolean;

  // Loading states
  loading: boolean;
  markingFeatureLoading: boolean;

  // Error states
  error: any;
  markingFeatureError: any;

  // Handlers
  fetchExploredFeatures: (forceRefetch?: boolean) => Promise<void>;
  markFeatureAsExplored: (featureType: string) => Promise<boolean>;
  isFeatureExplored: (featureType: string) => boolean;
  getModuleProgress: (moduleId: string) => ModuleProgress;
  getModuleExplorationStatus: (moduleId: string) => boolean;
  getAllModulesProgress: () => ModuleProgress;
  clearStore: () => void;
}

export type SetExploredFeaturesStore = (
  partial:
    | ExploredFeaturesState
    | Partial<ExploredFeaturesState>
    | ((
        state: ExploredFeaturesState,
      ) => ExploredFeaturesState | Partial<ExploredFeaturesState>),
  replace?: boolean | undefined,
) => void;

export type ExploredFeaturesStore = (state: ExploredFeaturesState) => any;
