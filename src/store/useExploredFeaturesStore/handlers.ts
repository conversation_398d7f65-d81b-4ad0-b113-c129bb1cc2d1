import { handleAsync } from '../../core/utils/asyncUtils';
import { EXPLORE_MODULE_FEATURES } from '../../modules/activation/constants/explore';
import ExploredFeaturesApi, { ExploredFeaturesApiInterface } from './api';
import {
  ExploredFeaturesData,
  ExploredFeaturesState,
  ModuleProgress,
  SetExploredFeaturesStore,
} from './types';

const _get = require('lodash/get');

/**
 * Handlers for Explored Features Store
 *
 * This class manages all business logic for explored features.
 * GraphQL is used only for data fetching - all caching is handled in Zustand.
 * No GraphQL cache invalidation or refetching is needed.
 */

interface ExploredFeaturesHandlersInterface {
  fetchExploredFeatures: (forceRefetch?: boolean) => Promise<void>;
  markFeatureAsExplored: (featureType: string) => Promise<boolean>;
  isFeatureExplored: (featureType: string) => boolean;
  getModuleProgress: (moduleId: string) => ModuleProgress;
  getModuleExplorationStatus: (moduleId: string) => boolean;
  getAllModulesProgress: () => ModuleProgress;
}

export default class ExploredFeaturesHandlers
  implements ExploredFeaturesHandlersInterface
{
  private api: ExploredFeaturesApiInterface;

  private setState: SetExploredFeaturesStore;

  private getState: () => ExploredFeaturesState;

  constructor(
    setState: SetExploredFeaturesStore,
    getState: () => ExploredFeaturesState,
  ) {
    this.api = new ExploredFeaturesApi();
    this.setState = setState;
    this.getState = getState;
  }

  fetchExploredFeatures = async (
    forceRefetch: boolean = false,
  ): Promise<void> => {
    const currentState = this.getState();

    // Prevent multiple simultaneous calls
    if (currentState.loading && !forceRefetch) {
      return;
    }

    // Prevent infinite retries - if there's an error and we're not forcing a refetch,
    // don't automatically retry
    if (currentState.error && !forceRefetch) {
      return;
    }

    this.setState((state: ExploredFeaturesState) => ({
      ...state,
      loading: true,
      error: null,
    }));

    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.fetchExploredFeatures,
    );

    if (error || resp?.error) {
      this.setState((state: ExploredFeaturesState) => ({
        ...state,
        error: error ?? resp?.error,
        loading: false,
      }));
      return;
    }

    const exploredFeaturesData = _get(
      resp,
      ['data', 'getExploredFeatures'],
      null,
    );

    const needToShowExploreFeatures = _get(
      resp,
      ['data', 'getExploredFeatures', 'needToShowExploreFeatures'],
      false,
    );

    const exploredFeaturesMap =
      this.createExploredFeaturesMap(exploredFeaturesData);

    this.setState((state: ExploredFeaturesState) => ({
      ...state,
      exploredFeaturesData,
      exploredFeaturesMap,
      needToShowExploreFeatures,
      loading: false,
      error: null,
    }));
  };

  markFeatureAsExplored = async (featureType: string): Promise<boolean> => {
    this.setState((state: ExploredFeaturesState) => ({
      ...state,
      markingFeatureLoading: true,
      markingFeatureError: null,
    }));

    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.markFeatureAsExplored,
      featureType,
    );

    if (error || resp?.error) {
      this.setState((state: ExploredFeaturesState) => ({
        ...state,
        markingFeatureError: error ?? resp?.error,
        markingFeatureLoading: false,
      }));
      return false;
    }

    const success = _get(resp, ['data', 'markFeatureAsExplored'], false);

    if (success) {
      // Update Zustand store directly - no need to refetch since we're not using GraphQL cache
      const currentState = this.getState();
      const updatedMap = {
        ...currentState.exploredFeaturesMap,
        [featureType]: true,
      };

      // Also update the exploredFeaturesData if it exists
      let updatedData = currentState.exploredFeaturesData;
      if (updatedData && !currentState.exploredFeaturesMap[featureType]) {
        updatedData = {
          ...updatedData,
          exploredFeatures: [
            ...updatedData.exploredFeatures,
            {
              featureType,
              exploredType: 'USER_TRIGGERED', // Default type
              timestamp: new Date().toISOString(),
            },
          ],
        };
      }

      this.setState((state: ExploredFeaturesState) => ({
        ...state,
        exploredFeaturesMap: updatedMap,
        exploredFeaturesData: updatedData,
        markingFeatureLoading: false,
        markingFeatureError: null,
      }));
    } else {
      this.setState((state: ExploredFeaturesState) => ({
        ...state,
        markingFeatureLoading: false,
      }));
    }

    return success;
  };

  isFeatureExplored = (featureType: string): boolean => {
    const state = this.getState();
    return state.exploredFeaturesMap[featureType] || false;
  };

  getModuleProgress = (moduleId: string): ModuleProgress => {
    const features =
      EXPLORE_MODULE_FEATURES[
        moduleId as keyof typeof EXPLORE_MODULE_FEATURES
      ] || [];
    const exploredFeatures = features.filter((feature) =>
      this.isFeatureExplored(feature),
    );

    return {
      explored: exploredFeatures.length,
      total: features.length,
      percentage:
        features.length > 0
          ? (exploredFeatures.length / features.length) * 100
          : 0,
    };
  };

  getModuleExplorationStatus = (moduleId: string): boolean => {
    const progress = this.getModuleProgress(moduleId);
    // Module is considered explored if all its features are explored
    return progress.total > 0 && progress.explored === progress.total;
  };

  getAllModulesProgress = (): ModuleProgress => {
    const allModules = Object.keys(EXPLORE_MODULE_FEATURES);
    let totalExplored = 0;
    let totalFeatures = 0;

    allModules.forEach((moduleId) => {
      const progress = this.getModuleProgress(moduleId);
      totalExplored += progress.explored;
      totalFeatures += progress.total;
    });

    return {
      explored: totalExplored,
      total: totalFeatures,
      percentage: totalFeatures > 0 ? (totalExplored / totalFeatures) * 100 : 0,
    };
  };

  private createExploredFeaturesMap = (
    exploredFeaturesData: ExploredFeaturesData | null,
  ): Record<string, boolean> => {
    if (!exploredFeaturesData?.exploredFeatures) return {};

    const map: Record<string, boolean> = {};
    exploredFeaturesData.exploredFeatures.forEach((feature) => {
      map[feature.featureType] = true;
    });
    return map;
  };
}
