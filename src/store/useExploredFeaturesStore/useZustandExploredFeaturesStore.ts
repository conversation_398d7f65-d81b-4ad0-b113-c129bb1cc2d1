/* eslint-disable no-param-reassign */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { ExploredFeaturesState } from './types';
import ExploredFeaturesHandlers from './handlers';
import { clearStore } from '../helpers';

const defaultConfig = {
  // Data
  exploredFeaturesData: null,
  exploredFeaturesMap: {},
  needToShowExploreFeatures: true,

  // Loading states
  loading: false,
  markingFeatureLoading: false,

  // Error states
  error: null,
  markingFeatureError: null,
};

const useZustandExploredFeaturesStore = create<ExploredFeaturesState>()(
  immer((set, get) => {
    const handlers = new ExploredFeaturesHandlers(set, get);

    return {
      ...defaultConfig,
      ...handlers,
      clearStore: () => {
        clearStore(set, defaultConfig);
      },
    };
  }),
);

export default useZustandExploredFeaturesStore;
