import * as queries from './graphql';

const PAGE_SIZE = 30;

export const getFriends = (page: number) =>
  global.getApolloClient().query({
    query: queries.GET_FRIENDS,
    variables: { page, pageSize: PAGE_SIZE },
  });

export const getFollowers = (page: number) =>
  global.getApolloClient().query({
    query: queries.GET_FOLLOWERS,
    variables: { page, pageSize: PAGE_SIZE },
  });

export const getFollowings = (page: number) =>
  global.getApolloClient().query({
    query: queries.GET_FOLLOWINGS,
    variables: { page, pageSize: PAGE_SIZE },
  });

export const getPendingFriendRequests = (page: number) =>
  global.getApolloClient().query({
    query: queries.GET_PENDING_FRIEND_REQUESTS,
    variables: { page, pageSize: PAGE_SIZE },
  });

export const acceptFriendRequest = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.ACCEPT_FRIEND_REQUEST,
    variables: { acceptRequestInput: { userId } },
  });

export const rejectFriendRequest = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.REJECT_FRIEND_REQUEST,
    variables: { rejectRequestInput: { userId } },
  });

export const sendFriendRequest = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.SEND_FRIEND_REQUEST,
    variables: { sendRequestInput: { userId } },
  });

export const withdrawFriendRequest = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.WITHDRAW_FRIEND_REQUEST,
    variables: { withdrawFriendRequestInput: { userId } },
  });

export const followUser = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.FOLLOW_USER,
    variables: { followUserInput: { userId } },
  });

export const unfollowUser = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.UNFOLLOW_USER,
    variables: { unfollowUserInput: { userId } },
  });

export const removeFriend = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.REMOVE_FRIEND,
    variables: { removeFriendInput: { userId } },
  });

export const removeFollower = (userId: string) =>
  global.getApolloClient().mutate({
    mutation: queries.REMOVE_FOLLOWER,
    variables: { removeFollowerInput: { userId } },
  });
