/* eslint-disable no-unused-vars */
/* eslint-disable import/no-unused-modules */
export enum MESSAGE_SOURCE_TYPE {
  SELF = 'self',
  OTHER = 'other',
}

/** Message * */
export type Message = any;
export type LastMessageId = string;

/** MessageGroup * */
export type MessageGroup = any;
export type MessageGroupId = string;

/** ChatState * */
export type ChatState = {
  messages: Record<MessageGroupId, Message[]>;
  lastMessageId: Record<MessageGroupId, LastMessageId | null>;
  messagesError: Record<MessageGroupId, Error | null>;
  messageGroupsShown: any[];
  isRead: boolean;
  isMessagesLoading: Record<MessageGroupId, boolean>;
  hasMoreMessages: Record<MessageGroupId, boolean>;
  messageGroups: Record<MessageGroupId, MessageGroup>;
  messageGroupNextPage: number;
  messageGroupHasMore: boolean;
  isMessageGroupsLoading: boolean;
  messageGroupsError: Error | null;
  messageGroupPage: number;
  updateLastMessageReadStateWebSocket: (
    _groupId: MessageGroupId,
    _userId: string,
  ) => Promise<void>;
  fetchMessages: (_groupId: MessageGroupId) => Promise<void>;
  fetchMessageGroups: () => Promise<void>;
  addMessage: (
    _message: Message,
    _groupId: MessageGroupId,
    _source: MESSAGE_SOURCE_TYPE,
  ) => Promise<void>;
  updateLastMessageRead: (
    _groupId: MessageGroupId,
    _userId: string,
  ) => Promise<void>;
  searchGroups: (_searchQuery: string) => Promise<void>;
  refreshMessageGroups: () => Promise<void>;
  clearStore: () => void;
};

export type ChatStore = (_state: ChatState) => any;

/** SetChatStore * */
export type SetChatStore = (
  _update: ChatState | ((_state: ChatState) => void),
) => void;

export type GetChatStore = () => ChatState;
