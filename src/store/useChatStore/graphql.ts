import { gql } from '@apollo/client';

export const GET_MESSAGES = gql`
  query GetMessagesByGroupId(
    $groupId: ID!
    $lastMessageId: ID
    $pageSize: Int
    $sortDirection: SortDirection
  ) {
    getMessagesByGroupId(
      groupId: $groupId
      lastMessageId: $lastMessageId
      pageSize: $pageSize
      sortDirection: $sortDirection
    ) {
      hasMore
      lastMessageId
      messages {
        _id
        attachment {
          type
          url
        }
        content
        createdAt
        groupId
        sender
        senderInfo {
          _id
          name
          username
          profileImageUrl
          rating
        }
      }
    }
  }
`;

export const GET_MESSAGE_GROUP = gql`
  query GetAllMessageGroups($input: GetAllMessageGroupsInput) {
    getAllMessageGroups(input: $input) {
      groups {
        _id
        groupName
        lastMessageRead {
          userId
          lastMessageRead
        }
        createdAt
        updatedAt
        groupType
        userInfoIfIndividual {
          _id
          name
          username
          profileImageUrl
          rating
        }
        members
        lastMessage {
          _id
          groupId
          content
          attachment {
            type
            url
          }
          createdAt
          sender
        }
      }
      nextPage
      hasMore
      isRead
    }
  }
`;

// updateLastMessageRead(groupId: ID!, lastMessageReadId: ID!): Boolean! @auth
export const UPDATE_LAST_MESSAGE_READ = gql`
  mutation UpdateLastMessageRead($groupId: ID!, $lastMessageReadId: ID!) {
    updateLastMessageRead(
      groupId: $groupId
      lastMessageReadId: $lastMessageReadId
    )
  }
`;
