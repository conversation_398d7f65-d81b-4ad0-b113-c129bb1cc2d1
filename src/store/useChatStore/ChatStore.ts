import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { ChatState, GetChatStore, SetChatStore } from './types';
import ChatHandlers from './handlers';
import { clearStore } from '../helpers';

const defaultConfig = {
  messages: {},
  messageGroups: {},
  lastMessageId: {},
  messageGroupsShown: [],
  isRead: true,
  hasMoreMessages: {},
  isMessagesLoading: {},
  messagesError: {},
  messageGroupPage: 1,
  isMessageGroupsLoading: false,
  messageGroupsError: null,
  messageGroupNextPage: 1,
  messageGroupHasMore: false,
};

// Ensure that zustand's immer middleware is correctly applied
const ChatStore = create<ChatState>()(
  immer((set: SetChatStore, get: GetChatStore) => {
    const handlers = new ChatHandlers(set, get);

    return {
      ...defaultConfig,
      ...handlers,
      clearStore: () => clearStore(set, defaultConfig),
    };
  }),
);

export default ChatStore;
