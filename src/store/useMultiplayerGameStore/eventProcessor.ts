import { GameEvent } from './types';

export class EventProcessor {
  private batchSize = 10;

  private batchTimeout = 200; // ms

  private maxQueueSize = 100;

  private deduplicationWindow = 100; // ms

  private eventHistory = new Map<string, number>();

  private processingTimer: NodeJS.Timeout | null = null;

  constructor(
    private onBatchProcess: (events: GameEvent[]) => void,
    private onQueueOverflow: () => void,
  ) {}

  addEvent(event: GameEvent, queue: GameEvent[]): GameEvent[] {
    // Deduplicate events within window
    const eventKey = `${event.type}_${event.userId}_${event.data?.questionId}`;
    const lastEventTime = this.eventHistory.get(eventKey);

    if (
      lastEventTime &&
      event.timestamp - lastEventTime < this.deduplicationWindow
    ) {
      return queue; // Skip duplicate
    }

    this.eventHistory.set(eventKey, event.timestamp);

    // Add to queue with priority sorting
    const newQueue = [...queue, event].sort((a, b) => b.priority - a.priority);

    // Manage queue size
    if (newQueue.length > this.maxQueueSize) {
      this.onQueueOverflow();
      return newQueue.slice(0, this.maxQueueSize);
    }

    // Schedule batch processing
    this.scheduleBatchProcessing();

    return newQueue;
  }

  processBatch(queue: GameEvent[]): {
    processedEvents: GameEvent[];
    remainingQueue: GameEvent[];
  } {
    if (queue.length === 0) {
      return { processedEvents: [], remainingQueue: [] };
    }

    // Take batch from queue
    const batchEvents = queue.slice(0, this.batchSize);
    const remainingQueue = queue.slice(this.batchSize);

    // Group events by type for efficient processing
    const groupedEvents = this.groupEventsByType(batchEvents);

    // Process each group
    const processedEvents = this.processGroupedEvents(groupedEvents);

    return { processedEvents, remainingQueue };
  }

  // Clean up old event history to prevent memory leaks
  cleanupHistory() {
    const cutoffTime = Date.now() - this.deduplicationWindow * 10;

    for (const [key, timestamp] of this.eventHistory.entries()) {
      if (timestamp < cutoffTime) {
        this.eventHistory.delete(key);
      }
    }
  }

  destroy() {
    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
    }
    this.eventHistory.clear();
  }

  private scheduleBatchProcessing() {
    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
    }

    this.processingTimer = setTimeout(() => {
      this.triggerBatchProcessing();
    }, this.batchTimeout);
  }

  private triggerBatchProcessing() {
    this.onBatchProcess([]);
  }

  private groupEventsByType(events: GameEvent[]): Record<string, GameEvent[]> {
    return events.reduce(
      (groups, event) => {
        if (!groups[event.type]) {
          groups[event.type] = [];
        }
        groups[event.type].push(event);
        return groups;
      },
      {} as Record<string, GameEvent[]>,
    );
  }

  private processGroupedEvents(
    groupedEvents: Record<string, GameEvent[]>,
  ): GameEvent[] {
    const processedEvents: GameEvent[] = [];

    // Process score updates in batch
    if (groupedEvents.scoreUpdate) {
      const latestScoreUpdates = this.getLatestScoreUpdates(
        groupedEvents.scoreUpdate,
      );
      processedEvents.push(...latestScoreUpdates);
    }

    // Process other events normally
    Object.entries(groupedEvents).forEach(([type, events]) => {
      if (type !== 'scoreUpdate') {
        processedEvents.push(...events);
      }
    });

    return processedEvents;
  }

  private getLatestScoreUpdates(scoreEvents: GameEvent[]): GameEvent[] {
    // Keep only the latest score update per user per question
    const latestUpdates = new Map<string, GameEvent>();

    scoreEvents.forEach((event) => {
      const key = `${event.userId}_${event.data?.questionId}`;
      const existing = latestUpdates.get(key);

      if (!existing || event.timestamp > existing.timestamp) {
        latestUpdates.set(key, event);
      }
    });

    return Array.from(latestUpdates.values());
  }
}

export class PerformanceMonitor {
  private renderTimes: number[] = [];

  private memoryUsage: number[] = [];

  private maxSamples = 50;

  recordRenderTime(time: number) {
    this.renderTimes.push(time);
    if (this.renderTimes.length > this.maxSamples) {
      this.renderTimes.shift();
    }
  }

  recordMemoryUsage() {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const { memory } = performance as any;
      this.memoryUsage.push(memory.usedJSHeapSize);
      if (this.memoryUsage.length > this.maxSamples) {
        this.memoryUsage.shift();
      }
    }
  }

  getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0;
    return (
      this.renderTimes.reduce((sum, time) => sum + time, 0) /
      this.renderTimes.length
    );
  }

  isPerformancePoor(): boolean {
    const avgRenderTime = this.getAverageRenderTime();
    return avgRenderTime > 16.67; // 60fps threshold
  }

  getDeviceCapability(): 'high' | 'medium' | 'low' {
    const avgRenderTime = this.getAverageRenderTime();

    if (avgRenderTime < 8) return 'high';
    if (avgRenderTime < 16.67) return 'medium';
    return 'low';
  }
}
