/* eslint-disable no-param-reassign */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { clearStore } from '../helpers';
import { MultiplayerGameState } from './types';
import MultiplayerGameHandlers from './handlers';

const defaultConfig: Omit<MultiplayerGameState, keyof MultiplayerGameHandlers> = {
  // Game Data
  gameId: null,
  game: null,
  questions: {},
  currentQuestionId: null,
  
  // Timing
  timing: {
    gameStartTime: 0,
    currentQuestionIndex: 0,
    currentQuestionStartTime: 0,
    timePerQuestion: 10,
    waitingTime: 5,
    timeRemaining: 0,
    currentPhase: 'INITIAL_WAITING_PHASE',
    serverTimeOffset: 0,
  },
  
  // Leaderboard
  playerScores: {},
  leaderboardVisible: false,
  lastAnsweredQuestion: null,
  
  // Event Processing
  eventQueue: [],
  eventBatches: [],
  isProcessingEvents: false,
  
  // Performance
  renderOptimizations: {
    skipAnimations: false,
    reducedUpdates: false,
    memoryMode: 'normal',
  },
  
  // Connection
  isConnected: false,
  lastSyncTime: 0,
  connectionQuality: 'good',
};

const useMultiplayerGameZustandStore = create<MultiplayerGameState>()(
  immer((set, get) => {
    const handlers = new MultiplayerGameHandlers(set, get);
    return {
      ...defaultConfig,
      ...handlers,
      clearStore: () => {
        handlers.clearGameState();
        clearStore(set, defaultConfig);
      },
    };
  }),
);

export default useMultiplayerGameZustandStore;
