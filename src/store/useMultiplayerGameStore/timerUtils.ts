import { GamePhase, GameTiming } from './types';

export class GameTimer {
  private timerId: NodeJS.Timeout | null = null;
  private callbacks: Set<() => void> = new Set();
  private isRunning = false;
  
  constructor(
    private updateCallback: (timing: GameTiming) => void,
    private serverTimeProvider: () => number
  ) {}

  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.tick();
  }

  stop() {
    this.isRunning = false;
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
  }

  private tick() {
    if (!this.isRunning) return;
    
    // Use requestAnimationFrame for smooth updates when available
    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(() => {
        this.callbacks.forEach(callback => callback());
        if (this.isRunning) {
          this.timerId = setTimeout(() => this.tick(), 100);
        }
      });
    } else {
      this.callbacks.forEach(callback => callback());
      if (this.isRunning) {
        this.timerId = setTimeout(() => this.tick(), 100);
      }
    }
  }

  addCallback(callback: () => void) {
    this.callbacks.add(callback);
  }

  removeCallback(callback: () => void) {
    this.callbacks.delete(callback);
  }

  destroy() {
    this.stop();
    this.callbacks.clear();
  }
}

export class TimingCalculator {
  constructor(
    private gameStartTime: number,
    private timePerQuestion: number,
    private waitingTime: number,
    private serverTimeProvider: () => number
  ) {}

  getCurrentTime(): number {
    return this.serverTimeProvider();
  }

  calculateCurrentQuestionIndex(): number {
    const currentTime = this.getCurrentTime();
    const timeSinceStart = currentTime - this.gameStartTime;
    const cycleTime = (this.timePerQuestion + this.waitingTime) * 1000;
    
    return Math.max(0, Math.floor(timeSinceStart / cycleTime));
  }

  calculateCurrentPhase(): keyof GamePhase {
    const currentTime = this.getCurrentTime();
    
    if (currentTime < this.gameStartTime) {
      return 'INITIAL_WAITING_PHASE';
    }

    const timeSinceStart = currentTime - this.gameStartTime;
    const cycleTime = (this.timePerQuestion + this.waitingTime) * 1000;
    const timeInCurrentCycle = timeSinceStart % cycleTime;
    
    if (timeInCurrentCycle < this.timePerQuestion * 1000) {
      return 'QUESTION_PHASE';
    }
    
    return 'WAITING_PHASE';
  }

  calculateTimeRemaining(): number {
    const currentTime = this.getCurrentTime();
    
    if (currentTime < this.gameStartTime) {
      return this.timePerQuestion * 1000;
    }

    const timeSinceStart = currentTime - this.gameStartTime;
    const cycleTime = (this.timePerQuestion + this.waitingTime) * 1000;
    const timeInCurrentCycle = timeSinceStart % cycleTime;
    
    if (timeInCurrentCycle < this.timePerQuestion * 1000) {
      return (this.timePerQuestion * 1000) - timeInCurrentCycle;
    }
    
    return 0;
  }

  calculateQuestionStartTime(questionIndex: number): number {
    const cycleTime = (this.timePerQuestion + this.waitingTime) * 1000;
    return this.gameStartTime + (questionIndex * cycleTime);
  }

  calculateWaitingPhaseEndTime(questionIndex: number): number {
    const questionStartTime = this.calculateQuestionStartTime(questionIndex);
    return questionStartTime + (this.timePerQuestion * 1000) + (this.waitingTime * 1000);
  }

  isQuestionActive(questionIndex: number): boolean {
    const currentTime = this.getCurrentTime();
    const questionStartTime = this.calculateQuestionStartTime(questionIndex);
    const questionEndTime = questionStartTime + (this.timePerQuestion * 1000);
    
    return currentTime >= questionStartTime && currentTime < questionEndTime;
  }

  getTimingData(): GameTiming {
    const currentQuestionIndex = this.calculateCurrentQuestionIndex();
    const currentPhase = this.calculateCurrentPhase();
    const timeRemaining = this.calculateTimeRemaining();
    const currentQuestionStartTime = this.calculateQuestionStartTime(currentQuestionIndex);
    
    return {
      gameStartTime: this.gameStartTime,
      currentQuestionIndex,
      currentQuestionStartTime,
      timePerQuestion: this.timePerQuestion,
      waitingTime: this.waitingTime,
      timeRemaining,
      currentPhase,
      serverTimeOffset: 0, // Will be set by the store
    };
  }
}

export class ServerTimeSync {
  private syncInterval: NodeJS.Timeout | null = null;
  private offset = 0;
  private lastSyncTime = 0;
  private syncFrequency = 30000; // 30 seconds

  constructor(
    private onOffsetUpdate: (offset: number) => void,
    private pingServer: () => Promise<{ serverTime: number; roundTripTime: number }>
  ) {}

  start() {
    this.performSync();
    this.syncInterval = setInterval(() => {
      this.performSync();
    }, this.syncFrequency);
  }

  stop() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  private async performSync() {
    try {
      const startTime = Date.now();
      const { serverTime, roundTripTime } = await this.pingServer();
      const endTime = Date.now();

      // Calculate offset accounting for network latency
      const networkDelay = (endTime - startTime) / 2;
      const adjustedServerTime = serverTime + networkDelay;
      const newOffset = adjustedServerTime - endTime;

      // Smooth offset changes to avoid sudden jumps
      if (Math.abs(newOffset - this.offset) > 1000) {
        // Large difference, apply immediately
        this.offset = newOffset;
      } else {
        // Small difference, smooth transition
        this.offset = this.offset * 0.9 + newOffset * 0.1;
      }

      this.lastSyncTime = Date.now();
      this.onOffsetUpdate(this.offset);

    } catch (error) {
      console.warn('Server time sync failed:', error);
    }
  }

  getCurrentServerTime(): number {
    return Date.now() + this.offset;
  }

  getOffset(): number {
    return this.offset;
  }

  getLastSyncTime(): number {
    return this.lastSyncTime;
  }

  destroy() {
    this.stop();
  }
}

// Utility functions for performance optimization
export const createCircularBuffer = <T>(size: number) => {
  const buffer: T[] = [];
  let index = 0;

  return {
    push: (item: T) => {
      buffer[index] = item;
      index = (index + 1) % size;
    },
    getAll: () => buffer.filter(item => item !== undefined),
    clear: () => {
      buffer.length = 0;
      index = 0;
    }
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;

  return ((...args: any[]) => {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  }) as T;
};
