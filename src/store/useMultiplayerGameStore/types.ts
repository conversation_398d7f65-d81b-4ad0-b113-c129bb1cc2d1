export interface GamePhase {
  QUESTION_PHASE: 'QUESTION_PHASE';
  WAITING_PHASE: 'WAITING_PHASE';
  INITIAL_WAITING_PHASE: 'INITIAL_WAITING_PHASE';
}

export interface QuestionState {
  id: string;
  question: any;
  hasSolved: boolean;
  incorrectAttempts: number;
  userAnswer?: string;
  isCorrect?: boolean;
  solvedAt?: number;
}

export interface PlayerScore {
  userId: string;
  username: string;
  avatar?: string;
  currentQuestionScore: number;
  totalScore: number;
  rank: number;
  lastUpdated: number;
}

export interface GameTiming {
  gameStartTime: number;
  currentQuestionIndex: number;
  currentQuestionStartTime: number;
  timePerQuestion: number;
  waitingTime: number;
  timeRemaining: number;
  currentPhase: keyof GamePhase;
  serverTimeOffset: number;
}

export interface EventBatch {
  events: GameEvent[];
  timestamp: number;
  processed: boolean;
}

export interface GameEvent {
  type: string;
  data: any;
  timestamp: number;
  priority: number;
  userId?: string;
}

export interface MultiplayerGameState {
  // Game Data
  gameId: string | null;
  game: any;
  questions: Record<string, QuestionState>;
  currentQuestionId: string | null;

  // Timing
  timing: GameTiming;

  // Leaderboard
  playerScores: Record<string, PlayerScore>;
  leaderboardVisible: boolean;
  lastAnsweredQuestion: any;

  // Event Processing
  eventQueue: GameEvent[];
  eventBatches: EventBatch[];
  isProcessingEvents: boolean;

  // Performance
  renderOptimizations: {
    skipAnimations: boolean;
    reducedUpdates: boolean;
    memoryMode: 'normal' | 'low';
  };

  // Connection
  isConnected: boolean;
  lastSyncTime: number;
  connectionQuality: 'good' | 'poor' | 'offline';

  // Actions
  initializeGame: (gameData: any) => void;
  updateGameTiming: (timing: Partial<GameTiming>) => void;
  addEvent: (event: GameEvent) => void;
  processBatchedEvents: () => void;
  updatePlayerScore: (userId: string, scoreData: Partial<PlayerScore>) => void;
  setCurrentQuestion: (questionId: string) => void;
  submitAnswer: (questionId: string, answer: string) => void;
  syncWithServer: (serverData: any) => void;
  optimizeForDevice: (deviceCapability: 'high' | 'medium' | 'low') => void;
  clearGameState: () => void;
}

export interface TimerState {
  // Server Time Sync
  serverTimeOffset: number;
  lastSyncTime: number;
  syncInterval: number;

  // Game Timing
  gameStartTime: number;
  timePerQuestion: number;
  waitingTime: number;
  totalQuestions: number;

  // Current State
  currentTime: number;
  currentQuestionIndex: number;
  currentPhase: keyof GamePhase;
  timeRemaining: number;

  // Actions
  initializeTimer: (gameData: any) => void;
  updateServerTime: (serverTime: number) => void;
  getCurrentTime: () => number;
  calculateCurrentQuestion: () => number;
  calculateTimeRemaining: () => number;
  calculateCurrentPhase: () => keyof GamePhase;
  startTimer: () => void;
  stopTimer: () => void;
}

export interface LeaderboardState {
  // Current Round Data
  currentRoundScores: Record<string, PlayerScore>;
  previousRoundScores: Record<string, PlayerScore>;

  // Batched Updates
  pendingUpdates: Record<string, Partial<PlayerScore>>;
  lastBatchTime: number;
  batchInterval: number;

  // Display State
  isVisible: boolean;
  sortedPlayers: PlayerScore[];
  userRank: number;

  // Performance
  maxDisplayPlayers: number;
  updateThrottle: number;

  // Actions
  addScoreUpdate: (userId: string, scoreData: Partial<PlayerScore>) => void;
  processBatchedUpdates: () => void;
  showLeaderboard: () => void;
  hideLeaderboard: () => void;
  clearPreviousRound: () => void;
  optimizeDisplay: (playerCount: number) => void;
}
