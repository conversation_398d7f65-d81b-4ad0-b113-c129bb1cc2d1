import { StateCreator } from 'zustand';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import {
  GameEvent,
  GameTiming,
  MultiplayerGameState,
  PlayerScore,
  QuestionState,
} from './types';
import { EventProcessor, PerformanceMonitor } from './eventProcessor';
import {
  GameTimer,
  ServerTimeSync,
  throttle,
  TimingCalculator,
} from './timerUtils';

export default class MultiplayerGameHandlers {
  private eventProcessor: EventProcessor;

  private performanceMonitor: PerformanceMonitor;

  private timingCalculator: TimingCalculator | null = null;

  private gameTimer: GameTimer | null = null;

  private serverTimeSync: ServerTimeSync | null = null;

  // Throttled functions
  private throttledUpdateTiming: (timing: Partial<GameTiming>) => void;

  private throttledProcessEvents: () => void;

  constructor(
    private set: StateCreator<
      MultiplayerGameState,
      [],
      [],
      MultiplayerGameState
    >['setState'],
    private get: StateCreator<
      MultiplayerGameState,
      [],
      [],
      MultiplayerGameState
    >['getState'],
  ) {
    this.eventProcessor = new EventProcessor(
      this.handleBatchedEvents.bind(this),
      this.handleQueueOverflow.bind(this),
    );

    this.performanceMonitor = new PerformanceMonitor();

    // Create throttled functions
    this.throttledUpdateTiming = throttle(
      this.updateGameTiming.bind(this),
      100,
    );
    this.throttledProcessEvents = throttle(
      this.processBatchedEvents.bind(this),
      200,
    );
  }

  initializeGame = (gameData: any) => {
    try {
      if (!gameData || !gameData.startTime || !gameData.questions) {
        console.error(
          'Invalid game data provided to initializeGame:',
          gameData,
        );
        return;
      }

      const startTime = new Date(gameData.startTime).getTime();
      const timePerQuestion = gameData.config?.maxTimePerQuestion || 10;
      const waitingTime = 5;

      // Initialize timing calculator
      this.timingCalculator = new TimingCalculator(
        startTime,
        timePerQuestion,
        waitingTime,
        getCurrentTimeWithOffset,
      );

      // Initialize server time sync
      this.serverTimeSync = new ServerTimeSync(
        this.handleServerTimeUpdate.bind(this),
        this.pingServer.bind(this),
      );

      // Initialize game timer
      this.gameTimer = new GameTimer(
        this.throttledUpdateTiming,
        getCurrentTimeWithOffset,
      );

      // Process questions
      const questions: Record<string, QuestionState> = {};
      if (gameData.questions && Array.isArray(gameData.questions)) {
        gameData.questions.forEach((questionObj: any) => {
          if (questionObj && questionObj.question && questionObj.question.id) {
            const { question } = questionObj;
            questions[question.id] = {
              id: question.id,
              question,
              hasSolved: false,
              incorrectAttempts: 0,
            };
          }
        });
      }

      // Initialize player scores from game data
      const initialPlayerScores: Record<string, any> = {};
      if (gameData.players && Array.isArray(gameData.players)) {
        gameData.players.forEach((player: any) => {
          if (player && player._id) {
            initialPlayerScores[player._id] = {
              userId: player._id,
              username: player.username || player.displayName || 'Unknown',
              avatar: player.avatar,
              currentQuestionScore: 0,
              totalScore: player.score || 0,
              rank: 0,
              lastUpdated: Date.now(),
            };
          }
        });
      }

      // Initialize state
      this.set((state) => {
        state.gameId = gameData._id;
        state.game = gameData;
        state.questions = questions;
        state.currentQuestionId = this.getCurrentQuestionId();
        state.timing = this.timingCalculator!.getTimingData();
        state.playerScores = initialPlayerScores;
        state.isConnected = true;
        state.lastSyncTime = Date.now();

        // Set initial leaderboard visibility based on phase
        const { currentPhase } = state.timing;
        state.leaderboardVisible = currentPhase === 'WAITING_PHASE';
      });

      // Start timers
      this.serverTimeSync.start();
      this.gameTimer.start();

      // Optimize for device capability
      const deviceCapability = this.performanceMonitor.getDeviceCapability();
      this.optimizeForDevice(deviceCapability);
    } catch (error) {
      console.error('Error initializing game:', error);
      // Set error state or fallback
      this.set((state) => {
        state.gameId = null;
        state.isConnected = false;
      });
    }
  };

  updateGameTiming = (timing: Partial<GameTiming>) => {
    if (!this.timingCalculator) return;

    const currentTiming = this.timingCalculator.getTimingData();
    const newTiming = { ...currentTiming, ...timing };

    this.set((state) => {
      const previousPhase = state.timing.currentPhase;
      state.timing = newTiming;

      // Update current question if needed
      const newQuestionId = this.getCurrentQuestionId();
      if (newQuestionId !== state.currentQuestionId) {
        state.currentQuestionId = newQuestionId;
      }

      // Handle phase transitions (check phase change regardless of question change)
      if (previousPhase !== newTiming.currentPhase) {
        if (
          previousPhase === 'WAITING_PHASE' &&
          newTiming.currentPhase === 'QUESTION_PHASE'
        ) {
          state.leaderboardVisible = false;
        } else if (
          previousPhase === 'QUESTION_PHASE' &&
          newTiming.currentPhase === 'WAITING_PHASE'
        ) {
          state.leaderboardVisible = true;
          state.lastAnsweredQuestion =
            state.questions[state.currentQuestionId!]?.question;
        }
      }

      // For debugging
      if (__DEV__) {
        console.log('Phase transition:', {
          previousPhase,
          newPhase: newTiming.currentPhase,
          leaderboardVisible: state.leaderboardVisible,
          currentQuestionId: state.currentQuestionId,
        });
      }
    });
  };

  addEvent = (event: GameEvent) => {
    this.set((state) => {
      state.eventQueue = this.eventProcessor.addEvent(event, state.eventQueue);
    });

    // Trigger processing if queue is getting large
    if (this.get().eventQueue.length > 5) {
      this.throttledProcessEvents();
    }
  };

  processBatchedEvents = () => {
    const state = this.get();
    if (state.isProcessingEvents || state.eventQueue.length === 0) return;

    this.set((state) => {
      state.isProcessingEvents = true;
    });

    const { processedEvents, remainingQueue } =
      this.eventProcessor.processBatch(state.eventQueue);

    this.set((state) => {
      state.eventQueue = remainingQueue;
      state.isProcessingEvents = false;
    });

    // Process the events
    this.handleBatchedEvents(processedEvents);
  };

  updatePlayerScore = (userId: string, scoreData: Partial<PlayerScore>) => {
    this.set((state) => {
      if (!state.playerScores[userId]) {
        state.playerScores[userId] = {
          userId,
          username: scoreData.username || 'Unknown',
          avatar: scoreData.avatar,
          currentQuestionScore: 0,
          totalScore: 0,
          rank: 0,
          lastUpdated: Date.now(),
        };
      }

      Object.assign(state.playerScores[userId], scoreData, {
        lastUpdated: Date.now(),
      });
    });

    this.updatePlayerRankings();
  };

  setCurrentQuestion = (questionId: string) => {
    this.set((state) => {
      state.currentQuestionId = questionId;
    });
  };

  submitAnswer = (questionId: string, answer: string) => {
    const timestamp = getCurrentTimeWithOffset();

    this.set((state) => {
      if (state.questions[questionId]) {
        state.questions[questionId].userAnswer = answer;
        state.questions[questionId].solvedAt = timestamp;
        // Note: hasSolved and isCorrect will be updated when server responds
      }
    });

    // Add event for processing
    this.addEvent({
      type: 'submitAnswer',
      data: { questionId, answer, timestamp },
      timestamp,
      priority: 10, // High priority
    });
  };

  syncWithServer = (serverData: any) => {
    this.set((state) => {
      // Update game state from server
      if (serverData.game) {
        state.game = { ...state.game, ...serverData.game };
      }

      // Update questions
      if (serverData.questions) {
        Object.entries(serverData.questions).forEach(
          ([id, questionData]: [string, any]) => {
            if (state.questions[id]) {
              state.questions[id] = { ...state.questions[id], ...questionData };
            }
          },
        );
      }

      // Update player scores
      if (serverData.playerScores) {
        Object.entries(serverData.playerScores).forEach(
          ([userId, scoreData]: [string, any]) => {
            state.playerScores[userId] = {
              ...state.playerScores[userId],
              ...scoreData,
            };
          },
        );
      }

      state.lastSyncTime = Date.now();
    });
  };

  optimizeForDevice = (deviceCapability: 'high' | 'medium' | 'low') => {
    this.set((state) => {
      switch (deviceCapability) {
        case 'low':
          state.renderOptimizations = {
            skipAnimations: true,
            reducedUpdates: true,
            memoryMode: 'low',
          };
          break;
        case 'medium':
          state.renderOptimizations = {
            skipAnimations: false,
            reducedUpdates: true,
            memoryMode: 'normal',
          };
          break;
        case 'high':
        default:
          state.renderOptimizations = {
            skipAnimations: false,
            reducedUpdates: false,
            memoryMode: 'normal',
          };
          break;
      }
    });
  };

  clearGameState = () => {
    // Clean up timers and processors
    this.gameTimer?.destroy();
    this.serverTimeSync?.destroy();
    this.eventProcessor.destroy();

    this.set((state) => {
      state.gameId = null;
      state.game = null;
      state.questions = {};
      state.currentQuestionId = null;
      state.playerScores = {};
      state.eventQueue = [];
      state.eventBatches = [];
      state.isProcessingEvents = false;
      state.leaderboardVisible = false;
      state.lastAnsweredQuestion = null;
    });
  };

  private handleBatchedEvents(events: GameEvent[]) {
    events.forEach((event) => {
      switch (event.type) {
        case 'scoreUpdate':
          this.handleScoreUpdate(event);
          break;
        case 'playerJoined':
          this.handlePlayerJoined(event);
          break;
        case 'playerLeft':
          this.handlePlayerLeft(event);
          break;
        case 'gameStateSync':
          this.handleGameStateSync(event);
          break;
        default:
          console.log('Unhandled event type:', event.type);
      }
    });
  }

  private handleScoreUpdate(event: GameEvent) {
    const { userId, score, questionId } = event.data;

    this.set((state) => {
      if (!state.playerScores[userId]) {
        state.playerScores[userId] = {
          userId,
          username: event.data.username || 'Unknown',
          avatar: event.data.avatar,
          currentQuestionScore: 0,
          totalScore: 0,
          rank: 0,
          lastUpdated: event.timestamp,
        };
      }

      const player = state.playerScores[userId];
      player.currentQuestionScore = score;
      player.totalScore = event.data.totalScore || player.totalScore;
      player.lastUpdated = event.timestamp;
    });

    // Update rankings
    this.updatePlayerRankings();
  }

  private handlePlayerJoined(event: GameEvent) {
    const { userId, username, avatar } = event.data;

    this.set((state) => {
      state.playerScores[userId] = {
        userId,
        username,
        avatar,
        currentQuestionScore: 0,
        totalScore: 0,
        rank: Object.keys(state.playerScores).length + 1,
        lastUpdated: event.timestamp,
      };
    });
  }

  private handlePlayerLeft(event: GameEvent) {
    const { userId } = event.data;

    this.set((state) => {
      delete state.playerScores[userId];
    });

    this.updatePlayerRankings();
  }

  private handleGameStateSync(event: GameEvent) {
    const { gameState } = event.data;
    this.syncWithServer(gameState);
  }

  private handleQueueOverflow() {
    console.warn('Event queue overflow, dropping old events');

    this.set((state) => {
      // Keep only high priority events
      state.eventQueue = state.eventQueue
        .filter((event) => event.priority > 5)
        .slice(-20); // Keep last 20 high priority events
    });
  }

  private updatePlayerRankings() {
    this.set((state) => {
      const players = Object.values(state.playerScores);
      players.sort((a, b) => b.totalScore - a.totalScore);

      players.forEach((player, index) => {
        state.playerScores[player.userId].rank = index + 1;
      });
    });
  }

  // Helper methods
  private getCurrentQuestionId(): string | null {
    try {
      if (!this.timingCalculator) return null;

      const questionIndex =
        this.timingCalculator.calculateCurrentQuestionIndex();
      const state = this.get();
      const questions = Object.values(state.questions);

      if (questionIndex < 0 || questionIndex >= questions.length) {
        return questions[0]?.id || null; // Return first question as fallback
      }

      return questions[questionIndex]?.id || null;
    } catch (error) {
      console.error('Error getting current question ID:', error);
      return null;
    }
  }

  private handleServerTimeUpdate(offset: number) {
    this.set((state) => {
      state.timing.serverTimeOffset = offset;
    });
  }

  private async pingServer(): Promise<{
    serverTime: number;
    roundTripTime: number;
  }> {
    // This would be implemented to ping your actual server
    // For now, return mock data
    return {
      serverTime: Date.now(),
      roundTripTime: 50,
    };
  }
}
