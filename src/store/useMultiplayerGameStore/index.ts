import { useShallow } from 'zustand/shallow';
import { MultiplayerGameState } from './types';
import useMultiplayerGameZustandStore from './useZustandMultiplayerGameStore';

type MultiplayerGameStore = (_state: MultiplayerGameState) => any;

const useMultiplayerGameStore = (func: MultiplayerGameStore) =>
  useMultiplayerGameZustandStore(useShallow(func));

export default useMultiplayerGameStore;
export type { MultiplayerGameState } from './types';
